import * as React from "react"
import { Slot } from "@radix-ui/react-slot"
import { cva, type VariantProps } from "class-variance-authority"
import { cn } from "@/lib/utils"
import { Loader2, AlertCircle, Check } from "lucide-react"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>Content, TooltipTrigger } from "@/components/ui/tooltip"

// Enhanced button variants with more options
const buttonVariants = cva(
  "inline-flex items-center justify-center whitespace-nowrap rounded-md text-sm font-medium ring-offset-background transition-all duration-200 focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 relative overflow-hidden",
  {
    variants: {
      variant: {
        default: "bg-primary text-primary-foreground hover:bg-primary/90 active:bg-primary/80",
        destructive: "bg-destructive text-destructive-foreground hover:bg-destructive/90 active:bg-destructive/80",
        outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        secondary: "bg-secondary text-secondary-foreground hover:bg-secondary/80 active:bg-secondary/70",
        ghost: "hover:bg-accent hover:text-accent-foreground active:bg-accent/80",
        link: "text-primary underline-offset-4 hover:underline focus:underline",
        success: "bg-green-600 text-white hover:bg-green-700 active:bg-green-800",
        warning: "bg-yellow-600 text-white hover:bg-yellow-700 active:bg-yellow-800",
        info: "bg-blue-600 text-white hover:bg-blue-700 active:bg-blue-800",
        gradient: "bg-gradient-to-r from-primary to-primary/80 text-primary-foreground hover:from-primary/90 hover:to-primary/70",
        glass: "backdrop-blur-sm bg-white/10 border border-white/20 text-white hover:bg-white/20",
      },
      size: {
        default: "h-10 px-4 py-2",
        sm: "h-9 rounded-md px-3 text-xs",
        lg: "h-11 rounded-md px-8 text-base",
        xl: "h-12 rounded-lg px-10 text-lg",
        icon: "h-10 w-10",
        "icon-sm": "h-8 w-8",
        "icon-lg": "h-12 w-12",
      },
      loading: {
        true: "cursor-wait",
        false: "",
      },
      fullWidth: {
        true: "w-full",
        false: "",
      },
      elevated: {
        true: "shadow-lg hover:shadow-xl transition-shadow",
        false: "",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "default",
      loading: false,
      fullWidth: false,
      elevated: false,
    },
  }
)

// Enhanced button props with comprehensive options
export interface EnhancedButtonProps
  extends React.ButtonHTMLAttributes<HTMLButtonElement>,
    VariantProps<typeof buttonVariants> {
  asChild?: boolean
  loading?: boolean
  loadingText?: string
  leftIcon?: React.ReactNode
  rightIcon?: React.ReactNode
  tooltip?: string
  tooltipSide?: "top" | "right" | "bottom" | "left"
  success?: boolean
  successText?: string
  successDuration?: number
  confirmAction?: boolean
  confirmText?: string
  onConfirm?: () => void
  ripple?: boolean
  analytics?: {
    event: string
    properties?: Record<string, any>
  }
  ariaLabel?: string
  ariaDescribedBy?: string
  testId?: string
}

// Ripple effect component
const RippleEffect: React.FC<{ x: number; y: number; size: number }> = ({ x, y, size }) => (
  <span
    className="absolute rounded-full bg-white/30 animate-ping pointer-events-none"
    style={{
      left: x - size / 2,
      top: y - size / 2,
      width: size,
      height: size,
    }}
  />
)

const EnhancedButton = React.forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  (
    {
      className,
      variant,
      size,
      asChild = false,
      loading = false,
      loadingText,
      leftIcon,
      rightIcon,
      tooltip,
      tooltipSide = "top",
      success = false,
      successText,
      successDuration = 2000,
      confirmAction = false,
      confirmText = "Are you sure?",
      onConfirm,
      ripple = false,
      analytics,
      ariaLabel,
      ariaDescribedBy,
      testId,
      children,
      onClick,
      disabled,
      fullWidth,
      elevated,
      ...props
    },
    ref
  ) => {
    const [isSuccess, setIsSuccess] = React.useState(false)
    const [showConfirm, setShowConfirm] = React.useState(false)
    const [ripples, setRipples] = React.useState<Array<{ id: number; x: number; y: number; size: number }>>([])
    const buttonRef = React.useRef<HTMLButtonElement>(null)
    const rippleId = React.useRef(0)

    // Combine refs
    React.useImperativeHandle(ref, () => buttonRef.current!)

    // Handle success state
    React.useEffect(() => {
      if (success && !isSuccess) {
        setIsSuccess(true)
        const timer = setTimeout(() => setIsSuccess(false), successDuration)
        return () => clearTimeout(timer)
      }
    }, [success, successDuration, isSuccess])

    // Handle ripple effect
    const handleRipple = React.useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
      if (!ripple || !buttonRef.current) return

      const rect = buttonRef.current.getBoundingClientRect()
      const size = Math.max(rect.width, rect.height)
      const x = event.clientX - rect.left
      const y = event.clientY - rect.top

      const newRipple = {
        id: rippleId.current++,
        x,
        y,
        size,
      }

      setRipples(prev => [...prev, newRipple])

      // Remove ripple after animation
      setTimeout(() => {
        setRipples(prev => prev.filter(r => r.id !== newRipple.id))
      }, 600)
    }, [ripple])

    // Handle click with analytics and confirmation
    const handleClick = React.useCallback((event: React.MouseEvent<HTMLButtonElement>) => {
      if (loading || disabled) return

      // Handle ripple effect
      handleRipple(event)

      // Handle confirmation
      if (confirmAction && !showConfirm) {
        setShowConfirm(true)
        return
      }

      // Reset confirmation state
      if (showConfirm) {
        setShowConfirm(false)
      }

      // Track analytics
      if (analytics && typeof window !== 'undefined') {
        // Google Analytics
        if ('gtag' in window) {
          (window as any).gtag('event', analytics.event, analytics.properties)
        }
        
        // Custom analytics
        if ('analytics' in window) {
          (window as any).analytics.track(analytics.event, analytics.properties)
        }
      }

      // Call appropriate handler
      if (confirmAction && onConfirm) {
        onConfirm()
      } else if (onClick) {
        onClick(event)
      }
    }, [loading, disabled, confirmAction, showConfirm, analytics, onConfirm, onClick, handleRipple])

    // Determine button content
    const getButtonContent = () => {
      if (loading) {
        return (
          <>
            <Loader2 className="mr-2 h-4 w-4 animate-spin" />
            {loadingText || "Loading..."}
          </>
        )
      }

      if (isSuccess) {
        return (
          <>
            <Check className="mr-2 h-4 w-4" />
            {successText || "Success!"}
          </>
        )
      }

      if (showConfirm) {
        return (
          <>
            <AlertCircle className="mr-2 h-4 w-4" />
            {confirmText}
          </>
        )
      }

      return (
        <>
          {leftIcon && <span className="mr-2">{leftIcon}</span>}
          {children}
          {rightIcon && <span className="ml-2">{rightIcon}</span>}
        </>
      )
    }

    // Determine button variant
    const getVariant = () => {
      if (isSuccess) return "success"
      if (showConfirm) return "warning"
      return variant
    }

    const Comp = asChild ? Slot : "button"
    const buttonElement = (
      <Comp
        ref={buttonRef}
        className={cn(buttonVariants({ 
          variant: getVariant(), 
          size, 
          loading, 
          fullWidth, 
          elevated, 
          className 
        }))}
        disabled={disabled || loading}
        onClick={handleClick}
        aria-label={ariaLabel}
        aria-describedby={ariaDescribedBy}
        data-testid={testId}
        {...props}
      >
        {getButtonContent()}
        {ripples.map(ripple => (
          <RippleEffect key={ripple.id} {...ripple} />
        ))}
      </Comp>
    )

    // Wrap with tooltip if provided
    if (tooltip) {
      return (
        <Tooltip>
          <TooltipTrigger asChild>
            {buttonElement}
          </TooltipTrigger>
          <TooltipContent side={tooltipSide}>
            {tooltip}
          </TooltipContent>
        </Tooltip>
      )
    }

    return buttonElement
  }
)

EnhancedButton.displayName = "EnhancedButton"

export { EnhancedButton, buttonVariants }
