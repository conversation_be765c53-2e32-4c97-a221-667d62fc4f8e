name: CI/CD Pipeline

on:
  push:
    branches: [ main, feat/integration-complete ]
  pull_request:
    branches: [ main ]

env:
  NODE_VERSION: '18'

jobs:
  lint:
    name: Lint & Format Check
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run ESLint
        run: npm run lint

      - name: TypeScript type check
        run: npm run type-check

  test-unit:
    name: Unit Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Run unit tests
        run: npm run test

      - name: Generate coverage report
        run: npm run test:coverage

      - name: Check coverage threshold (≥90%)
        run: |
          # Extract coverage percentage from coverage report
          if [ -f coverage/coverage-summary.json ]; then
            COVERAGE=$(node -e "console.log(JSON.parse(require('fs').readFileSync('coverage/coverage-summary.json')).total.lines.pct)")
            echo "Coverage: ${COVERAGE}%"
            if (( $(echo "$COVERAGE < 90" | bc -l) )); then
              echo "❌ Coverage ${COVERAGE}% is below 90% threshold"
              exit 1
            fi
            echo "✅ Coverage ${COVERAGE}% meets 90% threshold"
          else
            echo "⚠️ Coverage report not found, skipping threshold check"
          fi

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella

  test-e2e:
    name: E2E Tests
    runs-on: ubuntu-latest
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Install Playwright browsers
        run: npx playwright install --with-deps

      - name: Build application
        run: npm run build

      - name: Start application
        run: |
          npm run preview &
          sleep 10

      - name: Run E2E tests
        run: npx playwright test

      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: failure()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

  build:
    name: Build Application
    runs-on: ubuntu-latest
    needs: [lint, test-unit]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: npm run build

      - name: Upload build artifacts
        uses: actions/upload-artifact@v4
        with:
          name: build-artifacts
          path: |
            apps/web/dist/
            apps/api/dist/
          retention-days: 7

  coverage-report:
    name: Coverage Report
    runs-on: ubuntu-latest
    needs: [test-unit]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Generate coverage report
        run: npm run test:coverage

      - name: Check coverage threshold
        run: |
          echo "Checking coverage threshold of 85%"
          npm run test:coverage:check

  accessibility-check:
    name: Accessibility Check (axe ≥97)
    runs-on: ubuntu-latest
    needs: [build]
    timeout-minutes: 15
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Start preview server
        run: |
          cd apps/web
          npm run preview &
          echo $! > preview.pid
          npx wait-on http://localhost:4173 --timeout 60000

      - name: Run accessibility tests
        run: |
          cd apps/web
          npx playwright test tests/accessibility.test.ts

      - name: Stop preview server
        if: always()
        run: |
          if [ -f apps/web/preview.pid ]; then
            kill $(cat apps/web/preview.pid) || true
          fi

  performance-check:
    name: Performance Check (Lighthouse ≥90)
    runs-on: ubuntu-latest
    needs: [build]
    timeout-minutes: 20
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Run Lighthouse CI
        run: lhci autorun --config=.lighthouserc.json

  bundle-size-check:
    name: Bundle Size Check (≤900KB)
    runs-on: ubuntu-latest
    needs: [build]
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: build-artifacts

      - name: Check bundle size
        run: |
          cd apps/web
          BUNDLE_SIZE=$(find dist/assets -name "*.js" -exec gzip -c {} \; | wc -c)
          BUNDLE_SIZE_KB=$((BUNDLE_SIZE / 1024))

          echo "Bundle size: ${BUNDLE_SIZE_KB}KB (gzipped)"

          if [ "$BUNDLE_SIZE_KB" -gt 900 ]; then
            echo "❌ Bundle size ${BUNDLE_SIZE_KB}KB exceeds 900KB budget"
            exit 1
          fi

          echo "✅ Bundle size ${BUNDLE_SIZE_KB}KB within 900KB budget"

  quality-gates:
    name: Quality Gates Summary
    runs-on: ubuntu-latest
    needs: [test-unit, accessibility-check, performance-check, bundle-size-check]
    if: always()
    steps:
      - name: Check all quality gates
        run: |
          echo "## Quality Gates Summary" >> $GITHUB_STEP_SUMMARY
          echo "| Gate | Status |" >> $GITHUB_STEP_SUMMARY
          echo "|------|--------|" >> $GITHUB_STEP_SUMMARY
          echo "| Unit Tests & Coverage ≥90% | ${{ needs.test-unit.result == 'success' && '✅ PASS' || '❌ FAIL' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Accessibility Score ≥97% | ${{ needs.accessibility-check.result == 'success' && '✅ PASS' || '❌ FAIL' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Performance Score ≥90% | ${{ needs.performance-check.result == 'success' && '✅ PASS' || '❌ FAIL' }} |" >> $GITHUB_STEP_SUMMARY
          echo "| Bundle Size ≤900KB | ${{ needs.bundle-size-check.result == 'success' && '✅ PASS' || '❌ FAIL' }} |" >> $GITHUB_STEP_SUMMARY

          # Fail if any quality gate failed
          if [[ "${{ needs.test-unit.result }}" != "success" ||
                "${{ needs.accessibility-check.result }}" != "success" ||
                "${{ needs.performance-check.result }}" != "success" ||
                "${{ needs.bundle-size-check.result }}" != "success" ]]; then
            echo "❌ One or more quality gates failed"
            exit 1
          fi

          echo "✅ All quality gates passed!"
