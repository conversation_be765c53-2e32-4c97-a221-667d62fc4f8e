# Page snapshot

```yaml
- region "Notifications (F8)":
  - list
- banner:
  - button:
    - img
  - heading "Metamorphic Reactor" [level=6]
  - paragraph: Dashboard
  - button:
    - img
- img
- heading "Configuration Required" [level=3]
- button "Setup Wizard":
  - img
  - text: Setup Wizard
- button "Manual Setup"
- paragraph: Some services need configuration to work properly. Use the Setup Wizard for guided configuration or manual setup below.
- img
- text: Supabase
- img
- text: GitHub OAuth
- alert:
  - img
  - strong: "GitHub OAuth not configured:"
  - text: PR creation will be unavailable. Configure GitHub OAuth to enable automatic pull request creation.
- main:
  - heading "Reactor Prompt" [level=2]
  - button "Run Loop"
  - region "Code input editor":
    - code:
      - button "Find References"
      - button "Find References"
      - textbox "Code input editor"
  - navigation:
    - button "Editor":
      - img
      - paragraph: Editor
    - button "Enhanced":
      - img
      - paragraph: Enhanced
    - button "Diff":
      - img
      - paragraph: Diff
    - button "Console":
      - img
      - paragraph: Console
    - button "Stream":
      - img
      - paragraph: Stream
    - button "Menu":
      - img
      - paragraph: Menu
- alert
- alert
```