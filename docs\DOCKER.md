# Docker Deployment Guide

This guide covers running the Metamorphic Reactor using Docker containers for both development and production environments.

## Prerequisites

- **Docker**: Version 20.10+ with BuildKit support
- **Docker Compose**: Version 2.0+
- **Git**: For cloning the repository
- **8GB RAM**: Recommended for running all services

## Quick Start

### 1. <PERSON><PERSON> and Setup

```bash
git clone <repository-url>
cd code-alchemy-reactor
```

### 2. Environment Configuration

```bash
# Copy environment template
cp .env.docker .env

# Edit with your actual values
nano .env
```

**Required Environment Variables:**
```bash
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
OPENAI_API_KEY=sk-your-openai-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-key
```

### 3. Start Development Environment

```bash
# Using the helper script (recommended)
./scripts/docker-setup.sh dev

# Or using Docker Compose directly
docker-compose -f docker-compose.dev.yml up -d
```

### 4. Access Services

- **Web Frontend**: http://localhost:8080
- **API Backend**: http://localhost:3001
- **Redis**: localhost:6379
- **PostgreSQL**: localhost:5432

## Docker Setup Script

The `scripts/docker-setup.sh` script provides convenient commands for managing the Docker environment:

```bash
# Development environment with hot reloading
./scripts/docker-setup.sh dev

# Production environment
./scripts/docker-setup.sh prod

# Stop all services
./scripts/docker-setup.sh stop

# View logs
./scripts/docker-setup.sh logs
./scripts/docker-setup.sh logs api

# Health check
./scripts/docker-setup.sh health

# Clean up everything
./scripts/docker-setup.sh cleanup
```

## Architecture

### Services Overview

| Service | Purpose | Port | Health Check |
|---------|---------|------|--------------|
| **web** | React frontend (Nginx) | 8080 | `/health` |
| **api** | Express backend | 3001 | `/health` |
| **redis** | Caching & rate limiting | 6379 | `redis-cli ping` |
| **supabase** | PostgreSQL database | 5432 | `pg_isready` |
| **nginx** | Reverse proxy (prod) | 80/443 | `/health` |

### Network Architecture

```
┌─────────────────┐    ┌─────────────────┐
│   Web Frontend  │    │   API Backend   │
│   (React/Nginx) │    │   (Express)     │
│   Port: 8080    │    │   Port: 3001    │
└─────────┬───────┘    └─────────┬───────┘
          │                      │
          └──────────┬───────────┘
                     │
         ┌───────────▼───────────┐
         │   metamorphic-network │
         │   (Docker Bridge)     │
         └───────────┬───────────┘
                     │
    ┌────────────────┼────────────────┐
    │                │                │
┌───▼────┐    ┌──────▼──────┐    ┌───▼────┐
│ Redis  │    │  Supabase   │    │ Nginx  │
│ :6379  │    │  (Postgres) │    │ :80    │
└────────┘    │    :5432    │    └────────┘
              └─────────────┘
```

## Development vs Production

### Development Mode (`docker-compose.dev.yml`)

- **Hot Reloading**: Source code mounted as volumes
- **Debug Logging**: Verbose logging enabled
- **Faster Builds**: Uses builder stage for development
- **Local Database**: Includes Supabase container

**Features:**
- Live code reloading for both frontend and backend
- Development-optimized build targets
- Simplified networking
- Debug-friendly logging

### Production Mode (`docker-compose.yml`)

- **Optimized Images**: Multi-stage builds with minimal size
- **Security**: Non-root users, security headers
- **Performance**: Nginx with caching and compression
- **Monitoring**: Health checks and structured logging

**Features:**
- Multi-platform builds (AMD64/ARM64)
- Production-optimized Nginx configuration
- Comprehensive health checks
- Security hardening

## Container Details

### Web Frontend Container

**Base Image**: `nginx:alpine`
**Build**: Multi-stage (Node.js build → Nginx serve)
**Security**: Non-root user, security headers
**Features**:
- Gzip compression
- Static asset caching
- SPA routing support
- CSP headers

### API Backend Container

**Base Image**: `node:18-alpine`
**Build**: Multi-stage (build → production)
**Security**: Non-root user, minimal dependencies
**Features**:
- Production-only dependencies
- Health check endpoint
- Structured logging
- Graceful shutdown

### Redis Container

**Image**: `redis:7-alpine`
**Configuration**: LRU eviction, persistence enabled
**Memory**: 256MB limit (production), 128MB (development)
**Features**:
- Append-only file persistence
- Memory optimization
- Health monitoring

### Supabase Container

**Image**: `supabase/postgres:**********`
**Purpose**: Local development database
**Features**:
- Auto-initialization with schema
- Migration support
- Development-friendly configuration

## Troubleshooting

### Common Issues

#### 1. Port Conflicts
```bash
# Check what's using the ports
netstat -tulpn | grep :8080
netstat -tulpn | grep :3001

# Stop conflicting services
sudo systemctl stop nginx  # If running locally
```

#### 2. Permission Issues
```bash
# Fix script permissions
chmod +x scripts/docker-setup.sh

# Fix Docker permissions (Linux)
sudo usermod -aG docker $USER
newgrp docker
```

#### 3. Build Failures
```bash
# Clear Docker cache
docker builder prune -a

# Rebuild without cache
docker-compose build --no-cache
```

#### 4. Service Health Issues
```bash
# Check service logs
./scripts/docker-setup.sh logs api
./scripts/docker-setup.sh logs web

# Check container status
docker-compose ps

# Restart specific service
docker-compose restart api
```

### Debugging Commands

```bash
# Enter running container
docker exec -it metamorphic-api-dev bash
docker exec -it metamorphic-web-dev sh

# View real-time logs
docker-compose logs -f

# Check resource usage
docker stats

# Inspect container configuration
docker inspect metamorphic-api
```

## Performance Optimization

### Build Optimization

1. **Layer Caching**: Dependencies installed before source code copy
2. **Multi-stage Builds**: Separate build and runtime stages
3. **BuildKit**: Parallel builds and advanced caching
4. **Platform Targeting**: AMD64/ARM64 support

### Runtime Optimization

1. **Resource Limits**: Memory and CPU limits configured
2. **Health Checks**: Proper health monitoring
3. **Graceful Shutdown**: SIGTERM handling
4. **Log Management**: Structured logging with rotation

## Security Considerations

### Container Security

- **Non-root Users**: All containers run as non-root
- **Minimal Images**: Alpine-based images with minimal attack surface
- **Security Headers**: Comprehensive HTTP security headers
- **Network Isolation**: Services communicate via internal network

### Data Security

- **Environment Variables**: Sensitive data via environment variables
- **Volume Permissions**: Proper file permissions on mounted volumes
- **Secret Management**: No secrets in images or logs

## Monitoring and Logging

### Health Monitoring

All services include health checks:
- **Interval**: 30 seconds
- **Timeout**: 3-10 seconds
- **Retries**: 3-5 attempts
- **Start Period**: Grace period for startup

### Log Management

```bash
# View logs by service
docker-compose logs api
docker-compose logs web
docker-compose logs redis

# Follow logs in real-time
docker-compose logs -f --tail=100

# Export logs
docker-compose logs > application.log
```

## Deployment

### CI/CD Integration

The Docker setup integrates with GitHub Actions:

1. **Build**: Multi-platform images built on push
2. **Test**: Containers tested in CI environment
3. **Push**: Images pushed to GitHub Container Registry
4. **Deploy**: Automated deployment to staging/production

### Production Deployment

```bash
# Pull latest images
docker-compose pull

# Start production environment
./scripts/docker-setup.sh prod

# Verify deployment
./scripts/docker-setup.sh health
```

## Next Steps

1. **Configure Environment**: Set up your `.env` file with actual API keys
2. **Start Development**: Run `./scripts/docker-setup.sh dev`
3. **Test Services**: Verify all services are healthy
4. **Customize**: Modify configurations as needed for your environment

For additional help, see the main [README.md](../README.md) or check the [troubleshooting guide](./TROUBLESHOOTING.md).
