#!/bin/bash

# Docker Setup Script for Metamorphic Reactor
# Provides easy commands for Docker operations

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GRE<PERSON>}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if Docker is installed and running
check_docker() {
    if ! command -v docker &> /dev/null; then
        log_error "Docker is not installed. Please install Docker first."
        exit 1
    fi

    if ! docker info &> /dev/null; then
        log_error "Docker is not running. Please start Docker first."
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        log_error "Docker Compose is not installed. Please install Docker Compose first."
        exit 1
    fi
}

# Setup environment file
setup_env() {
    if [ ! -f .env ]; then
        log_info "Creating .env file from template..."
        cp .env.docker .env
        log_warning "Please edit .env file with your actual API keys and configuration"
        log_warning "Required: SUPABASE_URL, SUPABASE_ANON_KEY, SUPABASE_SERVICE_ROLE_KEY"
        log_warning "Optional: OPENAI_API_KEY, ANTHROPIC_API_KEY"
    else
        log_info ".env file already exists"
    fi
}

# Build images
build_images() {
    log_info "Building Docker images..."
    docker-compose build --parallel
    log_success "Images built successfully"
}

# Start development environment
start_dev() {
    log_info "Starting development environment..."
    setup_env
    docker-compose -f docker-compose.dev.yml up -d
    
    log_info "Waiting for services to be healthy..."
    sleep 10
    
    # Check service health
    if docker-compose -f docker-compose.dev.yml ps | grep -q "Up (healthy)"; then
        log_success "Development environment started successfully!"
        log_info "Services available at:"
        log_info "  - Web Frontend: http://localhost:8080"
        log_info "  - API Backend: http://localhost:3001"
        log_info "  - Redis: localhost:6379"
        log_info "  - PostgreSQL: localhost:5432"
    else
        log_error "Some services failed to start. Check logs with: docker-compose -f docker-compose.dev.yml logs"
    fi
}

# Start production environment
start_prod() {
    log_info "Starting production environment..."
    setup_env
    build_images
    docker-compose up -d
    
    log_info "Waiting for services to be healthy..."
    sleep 15
    
    # Check service health
    if docker-compose ps | grep -q "Up (healthy)"; then
        log_success "Production environment started successfully!"
        log_info "Services available at:"
        log_info "  - Web Frontend: http://localhost:8080"
        log_info "  - API Backend: http://localhost:3001"
        log_info "  - Nginx Proxy: http://localhost (if enabled)"
    else
        log_error "Some services failed to start. Check logs with: docker-compose logs"
    fi
}

# Stop services
stop_services() {
    log_info "Stopping services..."
    docker-compose down
    docker-compose -f docker-compose.dev.yml down
    log_success "Services stopped"
}

# Clean up everything
cleanup() {
    log_info "Cleaning up Docker resources..."
    docker-compose down -v --remove-orphans
    docker-compose -f docker-compose.dev.yml down -v --remove-orphans
    docker system prune -f
    log_success "Cleanup completed"
}

# Show logs
show_logs() {
    local service=${1:-}
    if [ -n "$service" ]; then
        log_info "Showing logs for $service..."
        docker-compose logs -f "$service"
    else
        log_info "Showing logs for all services..."
        docker-compose logs -f
    fi
}

# Health check
health_check() {
    log_info "Checking service health..."
    
    # Check if containers are running
    if docker-compose ps | grep -q "Up"; then
        log_success "Containers are running"
        
        # Check individual service health
        log_info "API Health: $(curl -s http://localhost:3001/health || echo 'FAILED')"
        log_info "Web Health: $(curl -s http://localhost:8080/health || echo 'FAILED')"
        log_info "Redis Health: $(docker exec metamorphic-redis redis-cli ping 2>/dev/null || echo 'FAILED')"
    else
        log_error "No containers are running"
    fi
}

# Show usage
usage() {
    echo "Docker Setup Script for Metamorphic Reactor"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  dev         Start development environment with hot reloading"
    echo "  prod        Start production environment"
    echo "  stop        Stop all services"
    echo "  build       Build Docker images"
    echo "  logs [svc]  Show logs (optionally for specific service)"
    echo "  health      Check service health"
    echo "  cleanup     Clean up all Docker resources"
    echo "  setup       Setup environment file"
    echo ""
    echo "Examples:"
    echo "  $0 dev                 # Start development environment"
    echo "  $0 logs api           # Show API logs"
    echo "  $0 health             # Check all services"
}

# Main script logic
main() {
    check_docker
    
    case "${1:-}" in
        "dev")
            start_dev
            ;;
        "prod")
            start_prod
            ;;
        "stop")
            stop_services
            ;;
        "build")
            build_images
            ;;
        "logs")
            show_logs "${2:-}"
            ;;
        "health")
            health_check
            ;;
        "cleanup")
            cleanup
            ;;
        "setup")
            setup_env
            ;;
        "help"|"-h"|"--help")
            usage
            ;;
        "")
            usage
            ;;
        *)
            log_error "Unknown command: $1"
            usage
            exit 1
            ;;
    esac
}

main "$@"
