import request from 'supertest';
import express from 'express';
import { requireAuth, requireAdmin, requireServiceRole, optionalAuth } from '../middleware/auth';
import { 
  generateTestJWT, 
  generateExpiredTestJWT, 
  generateInvalidTestJWT,
  getTestServiceKey,
  createAuthHeader,
  setupTestEnvironment,
  TEST_USERS,
  AUTH_TEST_SCENARIOS
} from './auth-helpers';

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn()
    }
  }))
}));

// Setup test environment
setupTestEnvironment();

// Create test app
const app = express();
app.use(express.json());

// Test routes with different auth requirements
app.get('/api/test/public', (req, res) => {
  res.json({ message: 'public endpoint', userId: req.userId });
});

app.get('/api/test/protected', requireAuth, (req, res) => {
  res.json({ 
    message: 'protected endpoint', 
    userId: req.userId,
    role: req.role 
  });
});

app.get('/api/test/admin', requireAuth, requireAdmin, (req, res) => {
  res.json({ 
    message: 'admin endpoint', 
    userId: req.userId,
    role: req.role 
  });
});

app.get('/api/test/service', requireServiceRole, (req, res) => {
  res.json({ 
    message: 'service endpoint', 
    userId: req.userId,
    role: req.role 
  });
});

app.get('/api/test/optional', optionalAuth, (req, res) => {
  res.json({ 
    message: 'optional auth endpoint', 
    userId: req.userId || 'anonymous',
    role: req.role || 'none'
  });
});

describe('Authentication Middleware', () => {
  let mockSupabaseClient: any;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Get the mocked Supabase client
    const { createClient } = require('@supabase/supabase-js');
    mockSupabaseClient = createClient();
  });

  describe('requireAuth middleware', () => {
    it('should accept valid JWT token', async () => {
      const token = generateTestJWT(TEST_USERS.regular);
      
      // Mock successful auth response
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.regular },
        error: null
      });

      const response = await request(app)
        .get('/api/test/protected')
        .set('Authorization', createAuthHeader(token))
        .expect(200);

      expect(response.body.userId).toBe(TEST_USERS.regular.id);
      expect(response.body.role).toBe(TEST_USERS.regular.role);
    });

    it('should reject missing authorization header', async () => {
      const response = await request(app)
        .get('/api/test/protected')
        .expect(401);

      expect(response.body.code).toBe('MISSING_AUTH_HEADER');
      expect(response.body.error).toBe('Authentication required');
    });

    it('should reject invalid authorization format', async () => {
      const response = await request(app)
        .get('/api/test/protected')
        .set('Authorization', 'InvalidFormat token-here')
        .expect(401);

      expect(response.body.code).toBe('INVALID_AUTH_FORMAT');
    });

    it('should reject missing JWT token', async () => {
      const response = await request(app)
        .get('/api/test/protected')
        .set('Authorization', 'Bearer ')
        .expect(401);

      expect(response.body.code).toBe('MISSING_JWT_TOKEN');
    });

    it('should reject expired JWT token', async () => {
      const token = generateExpiredTestJWT(TEST_USERS.regular);
      
      // Mock expired token error
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Token has expired' }
      });

      const response = await request(app)
        .get('/api/test/protected')
        .set('Authorization', createAuthHeader(token))
        .expect(401);

      expect(response.body.code).toBe('EXPIRED_TOKEN');
    });

    it('should reject invalid JWT token', async () => {
      const token = generateInvalidTestJWT();
      
      // Mock invalid token error
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token format' }
      });

      const response = await request(app)
        .get('/api/test/protected')
        .set('Authorization', createAuthHeader(token))
        .expect(401);

      expect(response.body.code).toBe('INVALID_TOKEN_FORMAT');
    });

    it('should handle Supabase service errors', async () => {
      const token = generateTestJWT(TEST_USERS.regular);
      
      // Mock service error
      mockSupabaseClient.auth.getUser.mockRejectedValue(new Error('Service unavailable'));

      const response = await request(app)
        .get('/api/test/protected')
        .set('Authorization', createAuthHeader(token))
        .expect(500);

      expect(response.body.code).toBe('AUTH_SERVICE_ERROR');
    });
  });

  describe('requireAdmin middleware', () => {
    it('should accept admin user', async () => {
      const token = generateTestJWT(TEST_USERS.admin);
      
      // Mock successful admin auth response
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.admin },
        error: null
      });

      const response = await request(app)
        .get('/api/test/admin')
        .set('Authorization', createAuthHeader(token))
        .expect(200);

      expect(response.body.userId).toBe(TEST_USERS.admin.id);
      expect(response.body.role).toBe('admin');
    });

    it('should reject regular user for admin endpoint', async () => {
      const token = generateTestJWT(TEST_USERS.regular);
      
      // Mock successful regular user auth response
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.regular },
        error: null
      });

      const response = await request(app)
        .get('/api/test/admin')
        .set('Authorization', createAuthHeader(token))
        .expect(403);

      expect(response.body.code).toBe('INSUFFICIENT_PERMISSIONS');
      expect(response.body.required_role).toBe('admin');
      expect(response.body.user_role).toBe('authenticated');
    });

    it('should reject unauthenticated user for admin endpoint', async () => {
      const response = await request(app)
        .get('/api/test/admin')
        .expect(401);

      expect(response.body.code).toBe('MISSING_AUTH_HEADER');
    });
  });

  describe('requireServiceRole middleware', () => {
    it('should accept valid service role key', async () => {
      const serviceKey = getTestServiceKey();

      const response = await request(app)
        .get('/api/test/service')
        .set('Authorization', createAuthHeader(serviceKey))
        .expect(200);

      expect(response.body.userId).toBe('service');
      expect(response.body.role).toBe('service_role');
    });

    it('should reject invalid service role key', async () => {
      const invalidKey = 'invalid-service-key';

      const response = await request(app)
        .get('/api/test/service')
        .set('Authorization', createAuthHeader(invalidKey))
        .expect(403);

      expect(response.body.code).toBe('INVALID_SERVICE_KEY');
    });

    it('should reject missing authorization for service endpoint', async () => {
      const response = await request(app)
        .get('/api/test/service')
        .expect(401);

      expect(response.body.code).toBe('MISSING_SERVICE_AUTH');
    });
  });

  describe('optionalAuth middleware', () => {
    it('should work with valid token', async () => {
      const token = generateTestJWT(TEST_USERS.regular);
      
      // Mock successful auth response
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.regular },
        error: null
      });

      const response = await request(app)
        .get('/api/test/optional')
        .set('Authorization', createAuthHeader(token))
        .expect(200);

      expect(response.body.userId).toBe(TEST_USERS.regular.id);
      expect(response.body.role).toBe(TEST_USERS.regular.role);
    });

    it('should work without token', async () => {
      const response = await request(app)
        .get('/api/test/optional')
        .expect(200);

      expect(response.body.userId).toBe('anonymous');
      expect(response.body.role).toBe('none');
    });

    it('should work with invalid token (graceful degradation)', async () => {
      const token = generateInvalidTestJWT();
      
      // Mock invalid token error
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: null },
        error: { message: 'Invalid token' }
      });

      const response = await request(app)
        .get('/api/test/optional')
        .set('Authorization', createAuthHeader(token))
        .expect(200);

      expect(response.body.userId).toBe('anonymous');
      expect(response.body.role).toBe('none');
    });
  });

  describe('Public endpoints', () => {
    it('should work without authentication', async () => {
      const response = await request(app)
        .get('/api/test/public')
        .expect(200);

      expect(response.body.message).toBe('public endpoint');
      expect(response.body.userId).toBeUndefined();
    });
  });
});
