{"name": "@metamorphic-reactor/api", "version": "1.0.0", "type": "module", "scripts": {"dev": "tsx watch src/index.ts", "build": "tsc", "start": "node dist/index.js", "test": "jest"}, "dependencies": {"@opentelemetry/api": "^1.9.0", "@opentelemetry/sdk-node": "^0.202.0", "@supabase/supabase-js": "^2.38.4", "cors": "^2.8.5", "express": "^4.18.2", "express-rate-limit": "^7.5.0", "fast-json-patch": "^3.1.1", "helmet": "^8.1.0", "ioredis": "^5.6.1", "jsonwebtoken": "^9.0.2", "pino": "^9.7.0", "pino-http": "^10.5.0", "pino-pretty": "^13.0.0", "rate-limiter-flexible": "^7.1.1", "ws": "^8.14.2", "zod": "^3.23.8"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/ioredis": "^4.28.10", "@types/jest": "^29.5.12", "@types/jsonwebtoken": "^9.0.5", "@types/supertest": "^6.0.2", "@types/ws": "^8.5.10", "jest": "^29.7.0", "supertest": "^6.3.4", "ts-jest": "^29.1.1", "tsx": "^4.7.0", "typescript": "^5.5.3"}}