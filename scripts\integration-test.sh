#!/bin/bash
# Comprehensive Integration Test for Metamorphic Reactor
# Tests all production features including AI agents, monitoring, caching, and security

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
TEST_RESULTS_DIR="${PROJECT_ROOT}/test-results"
LOG_FILE="${TEST_RESULTS_DIR}/integration-test-$(date +%Y%m%d-%H%M%S).log"

# Test configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:3001}"
WEB_BASE_URL="${WEB_BASE_URL:-http://localhost:3000}"
TIMEOUT=30
RETRY_COUNT=3

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# Test counters
TESTS_TOTAL=0
TESTS_PASSED=0
TESTS_FAILED=0

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Test function wrapper
run_test() {
    local test_name="$1"
    local test_function="$2"
    
    ((TESTS_TOTAL++))
    log INFO "Running test: $test_name"
    
    if $test_function; then
        ((TESTS_PASSED++))
        log INFO "✅ PASSED: $test_name"
        return 0
    else
        ((TESTS_FAILED++))
        log ERROR "❌ FAILED: $test_name"
        return 1
    fi
}

# HTTP request helper
http_request() {
    local method="$1"
    local url="$2"
    local data="${3:-}"
    local expected_status="${4:-200}"
    
    local response
    local status_code
    
    if [[ -n "$data" ]]; then
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            -H "Content-Type: application/json" \
            -d "$data" \
            --max-time "$TIMEOUT" \
            "$url" 2>/dev/null || echo -e "\n000")
    else
        response=$(curl -s -w "\n%{http_code}" -X "$method" \
            --max-time "$TIMEOUT" \
            "$url" 2>/dev/null || echo -e "\n000")
    fi
    
    status_code=$(echo "$response" | tail -n1)
    response_body=$(echo "$response" | head -n -1)
    
    if [[ "$status_code" == "$expected_status" ]]; then
        echo "$response_body"
        return 0
    else
        log ERROR "HTTP request failed: $method $url (expected $expected_status, got $status_code)"
        return 1
    fi
}

# Test: Basic health checks
test_health_checks() {
    log DEBUG "Testing health endpoints..."
    
    # API health check
    local api_health
    api_health=$(http_request GET "$API_BASE_URL/health")
    
    if ! echo "$api_health" | jq -e '.status == "ok"' >/dev/null 2>&1; then
        log ERROR "API health check failed"
        return 1
    fi
    
    # Web health check (if available)
    if curl -f -s "$WEB_BASE_URL/health" >/dev/null 2>&1; then
        log DEBUG "Web health check passed"
    else
        log WARN "Web health endpoint not available"
    fi
    
    return 0
}

# Test: Monitoring endpoints
test_monitoring() {
    log DEBUG "Testing monitoring endpoints..."
    
    # Metrics endpoint
    local metrics
    metrics=$(http_request GET "$API_BASE_URL/api/monitoring/metrics")
    
    if ! echo "$metrics" | jq -e '.system.memory.percentage' >/dev/null 2>&1; then
        log ERROR "Metrics endpoint failed"
        return 1
    fi
    
    # Health endpoint with details
    local health
    health=$(http_request GET "$API_BASE_URL/api/monitoring/health")
    
    if ! echo "$health" | jq -e '.services.api' >/dev/null 2>&1; then
        log ERROR "Detailed health endpoint failed"
        return 1
    fi
    
    return 0
}

# Test: Rate limiting
test_rate_limiting() {
    log DEBUG "Testing rate limiting..."
    
    # Get rate limit status
    local rate_status
    rate_status=$(http_request GET "$API_BASE_URL/api/rate-limit-status")
    
    if ! echo "$rate_status" | jq -e '.general.limit' >/dev/null 2>&1; then
        log ERROR "Rate limit status endpoint failed"
        return 1
    fi
    
    # Test rate limiting by making multiple requests
    local success_count=0
    local rate_limited=false
    
    for i in {1..10}; do
        if http_request GET "$API_BASE_URL/health" >/dev/null 2>&1; then
            ((success_count++))
        else
            rate_limited=true
            break
        fi
        sleep 0.1
    done
    
    log DEBUG "Made $success_count successful requests before rate limiting"
    return 0
}

# Test: Caching functionality
test_caching() {
    log DEBUG "Testing caching functionality..."
    
    # Get cache stats
    local cache_stats
    cache_stats=$(http_request GET "$API_BASE_URL/api/monitoring/cache-stats")
    
    if ! echo "$cache_stats" | jq -e '.metrics.totalRequests' >/dev/null 2>&1; then
        log ERROR "Cache stats endpoint failed"
        return 1
    fi
    
    # Test cache invalidation (if endpoint exists)
    local invalidate_data='{"pattern": "test:*"}'
    if http_request POST "$API_BASE_URL/api/monitoring/cache/invalidate" "$invalidate_data" >/dev/null 2>&1; then
        log DEBUG "Cache invalidation test passed"
    else
        log WARN "Cache invalidation endpoint not available or failed"
    fi
    
    return 0
}

# Test: AI endpoints (mock test)
test_ai_endpoints() {
    log DEBUG "Testing AI endpoints..."
    
    # Test AI metrics endpoint
    local ai_metrics
    ai_metrics=$(http_request GET "$API_BASE_URL/api/monitoring/ai-metrics")
    
    if ! echo "$ai_metrics" | jq -e '.metrics' >/dev/null 2>&1; then
        log ERROR "AI metrics endpoint failed"
        return 1
    fi
    
    # Note: We don't test actual AI operations to avoid costs
    # In a real test, you would test with mock providers
    log DEBUG "AI endpoints basic structure test passed"
    
    return 0
}

# Test: Security headers
test_security_headers() {
    log DEBUG "Testing security headers..."
    
    local headers
    headers=$(curl -I -s "$API_BASE_URL/health" | tr -d '\r')
    
    # Check for security headers
    if ! echo "$headers" | grep -i "x-content-type-options" >/dev/null; then
        log WARN "Missing X-Content-Type-Options header"
    fi
    
    if ! echo "$headers" | grep -i "x-frame-options" >/dev/null; then
        log WARN "Missing X-Frame-Options header"
    fi
    
    # Check CORS headers
    local cors_response
    cors_response=$(curl -s -H "Origin: http://localhost:3000" \
        -H "Access-Control-Request-Method: GET" \
        -H "Access-Control-Request-Headers: Content-Type" \
        -X OPTIONS "$API_BASE_URL/health")
    
    log DEBUG "Security headers test completed"
    return 0
}

# Test: Database connectivity (via API)
test_database_connectivity() {
    log DEBUG "Testing database connectivity..."
    
    # Test through API endpoints that require database
    local response
    response=$(http_request GET "$API_BASE_URL/api/monitoring/health")
    
    if echo "$response" | jq -e '.services' >/dev/null 2>&1; then
        log DEBUG "Database connectivity test passed"
        return 0
    else
        log ERROR "Database connectivity test failed"
        return 1
    fi
}

# Test: WebSocket connectivity
test_websocket() {
    log DEBUG "Testing WebSocket connectivity..."
    
    # Simple WebSocket connection test using curl
    if command -v wscat >/dev/null 2>&1; then
        # Use wscat if available
        timeout 5 wscat -c "ws://localhost:3001" -x "ping" >/dev/null 2>&1 || true
        log DEBUG "WebSocket test attempted with wscat"
    else
        # Skip WebSocket test if wscat not available
        log WARN "WebSocket test skipped (wscat not available)"
    fi
    
    return 0
}

# Test: Performance benchmarks
test_performance() {
    log DEBUG "Testing basic performance..."
    
    local start_time
    local end_time
    local response_time
    
    start_time=$(date +%s%N)
    http_request GET "$API_BASE_URL/health" >/dev/null
    end_time=$(date +%s%N)
    
    response_time=$(( (end_time - start_time) / 1000000 )) # Convert to milliseconds
    
    log DEBUG "API response time: ${response_time}ms"
    
    if [[ $response_time -gt 5000 ]]; then
        log WARN "High response time detected: ${response_time}ms"
    fi
    
    return 0
}

# Test: Error handling
test_error_handling() {
    log DEBUG "Testing error handling..."
    
    # Test 404 endpoint
    if http_request GET "$API_BASE_URL/nonexistent-endpoint" "" "404" >/dev/null 2>&1; then
        log DEBUG "404 error handling test passed"
    else
        log WARN "404 error handling test failed"
    fi
    
    # Test malformed JSON
    if http_request POST "$API_BASE_URL/api/monitoring/cache/invalidate" "invalid json" "400" >/dev/null 2>&1; then
        log DEBUG "Malformed JSON error handling test passed"
    else
        log WARN "Malformed JSON error handling test failed"
    fi
    
    return 0
}

# Test: Configuration validation
test_configuration() {
    log DEBUG "Testing configuration..."
    
    # Check if required environment variables are set
    local config_issues=0
    
    if [[ -z "${NODE_ENV:-}" ]]; then
        log WARN "NODE_ENV not set"
        ((config_issues++))
    fi
    
    if [[ -z "${REDIS_URL:-}" ]]; then
        log WARN "REDIS_URL not set"
        ((config_issues++))
    fi
    
    if [[ $config_issues -eq 0 ]]; then
        log DEBUG "Configuration validation passed"
        return 0
    else
        log WARN "Configuration validation found $config_issues issues"
        return 0  # Don't fail the test for warnings
    fi
}

# Main test execution
main() {
    log INFO "Starting comprehensive integration tests for Metamorphic Reactor"
    log INFO "API Base URL: $API_BASE_URL"
    log INFO "Web Base URL: $WEB_BASE_URL"
    
    # Create test results directory
    mkdir -p "$TEST_RESULTS_DIR"
    
    # Wait for services to be ready
    log INFO "Waiting for services to be ready..."
    sleep 10
    
    # Run all tests
    run_test "Health Checks" test_health_checks
    run_test "Monitoring Endpoints" test_monitoring
    run_test "Rate Limiting" test_rate_limiting
    run_test "Caching Functionality" test_caching
    run_test "AI Endpoints" test_ai_endpoints
    run_test "Security Headers" test_security_headers
    run_test "Database Connectivity" test_database_connectivity
    run_test "WebSocket Connectivity" test_websocket
    run_test "Performance Benchmarks" test_performance
    run_test "Error Handling" test_error_handling
    run_test "Configuration Validation" test_configuration
    
    # Generate test report
    log INFO "Integration test completed"
    log INFO "Tests Total: $TESTS_TOTAL"
    log INFO "Tests Passed: $TESTS_PASSED"
    log INFO "Tests Failed: $TESTS_FAILED"
    
    local success_rate
    success_rate=$(( (TESTS_PASSED * 100) / TESTS_TOTAL ))
    log INFO "Success Rate: ${success_rate}%"
    
    # Create JSON report
    cat > "$TEST_RESULTS_DIR/integration-test-report.json" <<EOF
{
  "timestamp": "$(date -u +%Y-%m-%dT%H:%M:%SZ)",
  "total": $TESTS_TOTAL,
  "passed": $TESTS_PASSED,
  "failed": $TESTS_FAILED,
  "success_rate": $success_rate,
  "api_base_url": "$API_BASE_URL",
  "web_base_url": "$WEB_BASE_URL"
}
EOF
    
    if [[ $TESTS_FAILED -eq 0 ]]; then
        log INFO "🎉 All integration tests passed!"
        exit 0
    else
        log ERROR "❌ $TESTS_FAILED integration tests failed"
        exit 1
    fi
}

# Run main function
main "$@"
