import { JSO<PERSON>atch, CritiqueRequest, CritiqueResult, AgentConfig, ProviderConfig } from './types.js';
import { AgentProvider } from './providers/AgentProvider.js';
import { ProviderFactory } from './providers/ProviderFactory.js';
import { ProviderFailover } from './utils/ProviderFailover.js';
import { CostGuard } from './utils/CostGuard.js';
import { TokenMonitor } from './utils/TokenMonitor.js';
import { StreamingManager } from './streaming/StreamingManager.js';

export class CritiqueAgent {
  private config: AgentConfig;
  private provider: AgentProvider;
  private failover?: ProviderFailover;
  private costGuard: CostGuard;
  private tokenMonitor: TokenMonitor;
  private streamingManager: StreamingManager;

  constructor(
    config: AgentConfig = {
      model: 'gemini-2.5',
      temperature: 0.3,
      maxTokens: 1500
    },
    costGuard?: CostGuard,
    tokenMonitor?: TokenMonitor,
    streamingManager?: StreamingManager
  ) {
    this.config = config;
    this.costGuard = costGuard || new CostGuard();
    this.tokenMonitor = tokenMonitor || new TokenMonitor();
    this.streamingManager = streamingManager || new StreamingManager();

    // Initialize provider
    if (config.provider) {
      this.provider = ProviderFactory.createProvider(config.provider);
    } else {
      // Legacy support - convert old config to new provider config
      const legacyConfig = this.convertLegacyConfig(config);
      this.provider = ProviderFactory.createProvider(legacyConfig);
    }
  }

  /**
   * Initialize with failover providers
   */
  initializeFailover(fallbackConfigs: ProviderConfig[]): void {
    if (this.config.provider) {
      this.failover = new ProviderFailover(
        this.config.provider,
        fallbackConfigs
      );
    }
  }

  async scorePatch(request: CritiqueRequest): Promise<CritiqueResult> {
    console.log(`[CritiqueAgent] Scoring patch with ${this.provider.getType()}:${this.provider.getModel()}`);
    console.log(`[CritiqueAgent] Original prompt: ${request.originalPrompt}`);

    // Check cost limits
    this.costGuard.checkCostLimit(0.15); // Estimate $0.15 for critique

    try {
      let result;

      if (this.failover) {
        // Use failover for enhanced reliability
        result = await this.failover.executeWithFailover(
          (provider) => provider.scorePatch(request),
          'scorePatch'
        );
      } else {
        // Direct provider execution
        result = await this.provider.scorePatch(request);
      }

      // Record metrics
      this.costGuard.recordCost(
        this.provider.getType(),
        this.provider.getModel(),
        'scorePatch',
        result.usage
      );

      this.tokenMonitor.recordUsage(
        this.provider.getType(),
        this.provider.getModel(),
        'scorePatch',
        result.usage,
        result.requestId
      );

      console.log(`[CritiqueAgent] Scored patch: ${result.data.score.toFixed(3)} (${result.data.isAcceptable ? 'ACCEPTABLE' : 'NEEDS_WORK'})`);
      console.log(`[CritiqueAgent] Cost: $${result.usage.cost.toFixed(4)}, Tokens: ${result.usage.totalTokens}`);

      return result.data;
    } catch (error) {
      console.error(`[CritiqueAgent] Error scoring patch:`, error);
      throw error;
    }
  }

  /**
   * Score patch with streaming
   */
  async *scorePatchStream(request: CritiqueRequest): AsyncGenerator<Partial<CritiqueResult>, CritiqueResult, unknown> {
    console.log(`[CritiqueAgent] Starting streaming critique`);

    this.costGuard.checkCostLimit(0.15);

    const requestId = `critique_${Date.now()}`;
    const streamGenerator = this.streamingManager.streamCritique(
      this.provider,
      request,
      requestId
    );

    try {
      for await (const chunk of streamGenerator) {
        yield {
          score: chunk.score,
          feedback: chunk.feedback,
          suggestions: chunk.suggestions,
          isAcceptable: chunk.isAcceptable,
        };
      }

      const finalResult = await streamGenerator.return({
        score: 0,
        feedback: '',
        suggestions: [],
        isAcceptable: false
      });
      return finalResult.value as CritiqueResult;
    } catch (error) {
      console.error(`[CritiqueAgent] Streaming error:`, error);
      throw error;
    }
  }

  /**
   * Get current provider information
   */
  getProviderInfo(): {
    type: string;
    model: string;
    cost: number;
    supportsStreaming: boolean;
  } {
    return {
      type: this.provider.getType(),
      model: this.provider.getModel(),
      cost: this.provider.getCost(),
      supportsStreaming: this.provider.supportsStreaming(),
    };
  }

  /**
   * Get cost and token statistics
   */
  getStats(): {
    totalCost: number;
    totalTokens: number;
    requestCount: number;
    averageCost: number;
  } {
    const costStats = this.costGuard.getStats();
    const tokenStats = this.tokenMonitor.getStats();

    return {
      totalCost: costStats.totalCost,
      totalTokens: tokenStats.totalTokens,
      requestCount: tokenStats.requestCount,
      averageCost: tokenStats.averageCost,
    };
  }

  /**
   * Validate a CritiqueResult object
   */
  async validateCritique(result: CritiqueResult): Promise<boolean> {
    // Basic validation
    if (typeof result.score !== 'number' || result.score < 0 || result.score > 1) {
      return false;
    }

    if (!result.feedback || typeof result.feedback !== 'string') {
      return false;
    }

    if (!Array.isArray(result.suggestions)) {
      return false;
    }

    if (typeof result.isAcceptable !== 'boolean') {
      return false;
    }

    return true;
  }

  private convertLegacyConfig(config: AgentConfig): ProviderConfig {
    // Convert legacy config to new provider config
    const modelMapping: Record<string, { type: 'openai' | 'vertex-ai'; model: string }> = {
      'gpt-4-turbo': { type: 'openai', model: 'gpt-4-turbo' },
      'gemini-2.5': { type: 'vertex-ai', model: 'gemini-2.5-flash' },
    };

    const mapping = modelMapping[config.model || 'gpt-4-turbo'];

    return {
      type: mapping.type,
      model: mapping.model,
      temperature: config.temperature || 0.3,
      maxTokens: config.maxTokens || 1500,
      systemPrompt: 'You are a code critique agent. Evaluate JSON patches for correctness, completeness, quality, and safety.',
    };
  }

}
