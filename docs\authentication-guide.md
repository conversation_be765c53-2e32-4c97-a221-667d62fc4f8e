# 🔐 Authentication Guide

## Overview

The Metamorphic Reactor API uses Supabase authentication with JWT tokens to secure all protected endpoints. This guide covers authentication implementation, testing, and troubleshooting.

## Authentication Architecture

### JWT Token Validation
- **Provider**: Supabase Auth
- **Algorithm**: HS256 with Supabase JWT secret
- **Validation**: Server-side using `supabase.auth.getUser()`
- **User Context**: Extracted from validated JWT claims

### Middleware Structure
```typescript
// apps/api/src/middleware/auth.ts
export const requireAuth = async (req, res, next) => {
  // 1. Extract JWT from Authorization header
  // 2. Validate token with Supabase
  // 3. Set req.userId, req.user, req.role
  // 4. Handle errors with proper status codes
};
```

## Authentication Types

### 1. User Authentication (`requireAuth`)
**Used for**: Standard user endpoints
**Token**: Supabase JWT from user login
**Context**: Sets `req.userId`, `req.user`, `req.role`

```typescript
// Example usage
router.get('/api/settings', requireAuth, async (req, res) => {
  const userId = req.userId; // Available after auth
  const userRole = req.role; // 'authenticated' or 'admin'
});
```

### 2. Admin Authentication (`requireAdmin`)
**Used for**: Admin-only operations
**Requirements**: Must be used after `requireAuth`
**Role Check**: Validates `req.role === 'admin'`

```typescript
// Example usage
router.post('/api/admin/users', requireAuth, requireAdmin, async (req, res) => {
  // Only admin users can access this endpoint
});
```

### 3. Service Role Authentication (`requireServiceRole`)
**Used for**: Internal service operations
**Token**: Supabase service role key
**Context**: Sets `req.role = 'service_role'`, `req.userId = 'service'`

```typescript
// Example usage
router.get('/api/queue/detailed', requireServiceRole, async (req, res) => {
  // Only service role key can access this endpoint
});
```

### 4. Optional Authentication (`optionalAuth`)
**Used for**: Endpoints that work for both authenticated and anonymous users
**Behavior**: Continues without error if no token provided

```typescript
// Example usage
router.get('/api/public-data', optionalAuth, async (req, res) => {
  const userId = req.userId || 'anonymous';
  // Works with or without authentication
});
```

## Protected Endpoints

### User Authentication Required (29 endpoints)

#### Onboarding API
- `GET /api/onboarding/progress`
- `POST /api/onboarding/progress`
- `POST /api/onboarding/api-keys`
- `GET /api/onboarding/status`
- `POST /api/onboarding/skip`
- `POST /api/onboarding/reset`

#### Billing API
- `GET /api/billing/subscription`
- `POST /api/billing/create-subscription`
- `GET /api/billing/usage`
- `POST /api/billing/record-usage`
- `GET /api/billing/usage-limit`
- `POST /api/billing/create-checkout-session`
- `GET /api/billing/detailed`

#### Settings API
- `GET /api/settings`
- `PUT /api/settings`
- `DELETE /api/settings`

#### Notifications API
- `GET /api/notifications`
- `POST /api/notifications`
- `PUT /api/notifications/mark-all-read`
- `DELETE /api/notifications`
- `PUT /api/notifications/:id/read`
- `POST /api/notifications/:id/action`
- `DELETE /api/notifications/:id`

#### Repositories API
- `GET /api/repositories`
- `POST /api/repositories`
- `PUT /api/repositories/:id`
- `DELETE /api/repositories/:id`
- `POST /api/repositories/:id/scan`
- `GET /api/repositories/:id/scans`
- `GET /api/repositories/:id/opportunities`

#### Auto-PR API
- `POST /api/auto-pr/create`
- `GET /api/auto-pr/active`
- `GET /api/auto-pr/plan/:planId`
- `POST /api/auto-pr/cleanup`

### Service Role Required (9 endpoints)

#### Secrets Management
- `POST /api/secrets`
- `GET /api/secrets`
- `GET /api/secrets/:keyName`
- `PUT /api/secrets/:keyName`
- `DELETE /api/secrets/:keyName`
- `POST /api/secrets/rotate`

#### Queue Administration
- `GET /api/queue/detailed`
- `POST /api/queue/clear`

### Public Endpoints (6 endpoints)
- `GET /api/health`
- `GET /api/billing/plans`
- `POST /api/billing/webhook`
- `GET /api/queue/status`
- `GET /api/queue/health`
- `GET /api/version`

## Error Handling

### Authentication Errors (401)
```json
{
  "error": "Authentication failed",
  "message": "Token has expired",
  "code": "EXPIRED_TOKEN"
}
```

### Authorization Errors (403)
```json
{
  "error": "Insufficient permissions",
  "message": "Admin role required for this operation",
  "code": "INSUFFICIENT_PERMISSIONS",
  "required_role": "admin",
  "user_role": "authenticated"
}
```

### Service Errors (500)
```json
{
  "error": "Authentication service error",
  "message": "Internal server error during authentication",
  "code": "AUTH_SERVICE_ERROR"
}
```

## Testing Authentication

### Test Utilities
```typescript
// apps/api/src/__tests__/auth-helpers.ts
import { generateTestJWT, TEST_USERS } from './auth-helpers';

// Generate valid test token
const token = generateTestJWT(TEST_USERS.regular);

// Generate admin test token
const adminToken = generateTestJWT(TEST_USERS.admin);

// Generate expired token
const expiredToken = generateExpiredTestJWT(TEST_USERS.regular);
```

### Test Examples
```typescript
describe('Authentication Tests', () => {
  it('should accept valid JWT token', async () => {
    const token = generateTestJWT(TEST_USERS.regular);
    
    const response = await request(app)
      .get('/api/settings')
      .set('Authorization', `Bearer ${token}`)
      .expect(200);
      
    expect(response.body.success).toBe(true);
  });
  
  it('should reject expired token', async () => {
    const token = generateExpiredTestJWT(TEST_USERS.regular);
    
    const response = await request(app)
      .get('/api/settings')
      .set('Authorization', `Bearer ${token}`)
      .expect(401);
      
    expect(response.body.code).toBe('EXPIRED_TOKEN');
  });
});
```

## Client Implementation

### JavaScript/TypeScript
```typescript
// Set up authenticated requests
const supabase = createClient(url, anonKey);

// Get user session
const { data: { session } } = await supabase.auth.getSession();

// Make authenticated API request
const response = await fetch('/api/settings', {
  headers: {
    'Authorization': `Bearer ${session.access_token}`,
    'Content-Type': 'application/json'
  }
});
```

### cURL Examples
```bash
# User authentication
curl -X GET http://localhost:3001/api/settings \
  -H "Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."

# Service role authentication
curl -X GET http://localhost:3001/api/queue/detailed \
  -H "Authorization: Bearer your-service-role-key"
```

## Security Best Practices

### Token Management
- ✅ **Never expose JWT secret** in client-side code
- ✅ **Store JWT secret** securely in environment variables
- ✅ **Validate token expiration** on every request
- ✅ **Use HTTPS only** for JWT transmission
- ✅ **Implement token refresh** for long-lived sessions

### Error Handling
- ✅ **Return consistent error format** across all endpoints
- ✅ **Log authentication failures** for security monitoring
- ✅ **Rate limit authentication endpoints** to prevent brute force
- ✅ **Don't expose sensitive details** in error messages

### Role-Based Access Control
- ✅ **Validate user roles** for admin operations
- ✅ **Use service role key** for internal operations only
- ✅ **Implement least privilege** principle
- ✅ **Audit admin access** regularly

## Troubleshooting

### Common Issues

#### "Missing Authorization header"
- **Cause**: No `Authorization` header in request
- **Solution**: Add `Authorization: Bearer <token>` header

#### "Invalid token format or signature"
- **Cause**: Malformed JWT or wrong secret
- **Solution**: Verify JWT structure and Supabase configuration

#### "Token has expired"
- **Cause**: JWT token past expiration time
- **Solution**: Refresh token or re-authenticate user

#### "User not found"
- **Cause**: Valid JWT but user doesn't exist in Supabase
- **Solution**: Check user exists in Supabase Auth

#### "Insufficient permissions"
- **Cause**: User lacks required role for endpoint
- **Solution**: Verify user has correct role in app_metadata

### Debug Mode
Set `NODE_ENV=development` to enable detailed authentication logging:
```
✅ Auth: User test-user-123 authenticated with role: authenticated
🔑 Admin: User admin-456 accessing admin endpoint
```

## Migration Notes

### From Mock Authentication
The system was migrated from hardcoded user IDs to proper JWT validation:

**Before (Insecure)**:
```typescript
req.userId = 'mock-user-id'; // ❌ Security bypass
```

**After (Secure)**:
```typescript
const { data, error } = await supabase.auth.getUser(token);
req.userId = data.user.id; // ✅ Validated user ID
```

### Breaking Changes
- All protected endpoints now require valid JWT tokens
- Mock user IDs no longer work
- Tests must use proper JWT tokens
- Service endpoints require service role key

## Support

For authentication issues:
1. Check the [API Documentation](./API.md)
2. Review [Error Codes](./API.md#error-codes)
3. Test with [auth-helpers.ts](../apps/api/src/__tests__/auth-helpers.ts)
4. Enable debug logging in development
