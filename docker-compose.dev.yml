# Docker Compose for Development
# Simplified setup for local development with hot reloading

version: '3.8'

services:
  # Redis for caching and rate limiting
  redis:
    image: redis:7-alpine
    container_name: metamorphic-redis-dev
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_dev_data:/data
    command: redis-server --appendonly yes --maxmemory 128mb --maxmemory-policy allkeys-lru
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 3s
      retries: 3
    networks:
      - metamorphic-dev-network

  # Supabase (local development)
  supabase:
    image: supabase/postgres:**********
    container_name: metamorphic-supabase-dev
    restart: unless-stopped
    ports:
      - "5432:5432"
    environment:
      POSTGRES_DB: postgres
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_HOST_AUTH_METHOD: trust
    volumes:
      - supabase_dev_data:/var/lib/postgresql/data
      - ./supabase/schema.sql:/docker-entrypoint-initdb.d/01-schema.sql:ro
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - metamorphic-dev-network

  # API Backend (Development mode with hot reloading)
  api-dev:
    build:
      context: .
      dockerfile: apps/api/Dockerfile
      target: builder
    container_name: metamorphic-api-dev
    restart: unless-stopped
    ports:
      - "3001:3001"
    environment:
      NODE_ENV: development
      PORT: 3001
      SUPABASE_URL: http://supabase:5432
      REDIS_URL: redis://redis:6379
      CORS_ORIGIN: http://localhost:8080
    volumes:
      - ./apps/api/src:/app/apps/api/src:ro
      - ./packages/agents/src:/app/packages/agents/src:ro
      - api_dev_logs:/app/logs
    depends_on:
      redis:
        condition: service_healthy
      supabase:
        condition: service_healthy
    command: ["npm", "run", "dev", "--workspace=apps/api"]
    networks:
      - metamorphic-dev-network

  # Web Frontend (Development mode with hot reloading)
  web-dev:
    build:
      context: .
      dockerfile: apps/web/Dockerfile
      target: builder
    container_name: metamorphic-web-dev
    restart: unless-stopped
    ports:
      - "8080:8080"
    environment:
      NODE_ENV: development
      VITE_API_URL: http://localhost:3001
    volumes:
      - ./apps/web/src:/app/apps/web/src:ro
      - ./apps/web/public:/app/apps/web/public:ro
    depends_on:
      - api-dev
    command: ["npm", "run", "dev", "--workspace=apps/web"]
    networks:
      - metamorphic-dev-network

volumes:
  redis_dev_data:
    driver: local
  supabase_dev_data:
    driver: local
  api_dev_logs:
    driver: local

networks:
  metamorphic-dev-network:
    driver: bridge
