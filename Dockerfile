# Multi-stage Docker build for Metamorphic Reactor
# Optimized for production with security, performance, and size considerations

# ================================
# Stage 1: Base Dependencies
# ================================
FROM node:20-alpine AS base

# Install security updates and required packages
RUN apk update && apk upgrade && \
    apk add --no-cache \
    dumb-init \
    curl \
    git \
    python3 \
    make \
    g++ \
    && rm -rf /var/cache/apk/*

# Create non-root user for security
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

# Set working directory
WORKDIR /app

# Copy package files for dependency installation
COPY package*.json ./
COPY packages/agents/package*.json ./packages/agents/
COPY apps/web/package*.json ./apps/web/
COPY apps/api/package*.json ./apps/api/

# ================================
# Stage 2: Dependencies
# ================================
FROM base AS deps

# Install all dependencies (including devDependencies for build)
RUN npm ci --include=dev

# ================================
# Stage 3: Build Stage
# ================================
FROM base AS builder

# Copy dependencies from deps stage
COPY --from=deps /app/node_modules ./node_modules
COPY --from=deps /app/packages/agents/node_modules ./packages/agents/node_modules
COPY --from=deps /app/apps/web/node_modules ./apps/web/node_modules
COPY --from=deps /app/apps/api/node_modules ./apps/api/node_modules

# Copy source code
COPY . .

# Build arguments for environment-specific builds
ARG NODE_ENV=production
ARG BUILD_TARGET=production
ARG VITE_API_URL
ARG VITE_SUPABASE_URL
ARG VITE_SUPABASE_ANON_KEY

# Set environment variables for build
ENV NODE_ENV=${NODE_ENV}
ENV NEXT_TELEMETRY_DISABLED=1

# Build the applications
RUN npm run build

# Remove development dependencies to reduce size
RUN npm ci --omit=dev && npm cache clean --force

# ================================
# Stage 4: Production Runtime
# ================================
FROM node:20-alpine AS runner

# Install runtime security updates
RUN apk update && apk upgrade && \
    apk add --no-cache dumb-init curl && \
    rm -rf /var/cache/apk/*

# Create non-root user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nextjs -u 1001

WORKDIR /app

# Copy built application and dependencies
COPY --from=builder --chown=nextjs:nodejs /app/dist ./dist
COPY --from=builder --chown=nextjs:nodejs /app/node_modules ./node_modules
COPY --from=builder --chown=nextjs:nodejs /app/package*.json ./

# Copy built packages
COPY --from=builder --chown=nextjs:nodejs /app/packages/agents/dist ./packages/agents/dist
COPY --from=builder --chown=nextjs:nodejs /app/packages/agents/package*.json ./packages/agents/

# Copy built web application
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/dist ./apps/web/dist
COPY --from=builder --chown=nextjs:nodejs /app/apps/web/package*.json ./apps/web/

# Copy built API application
COPY --from=builder --chown=nextjs:nodejs /app/apps/api/dist ./apps/api/dist
COPY --from=builder --chown=nextjs:nodejs /app/apps/api/package*.json ./apps/api/

# Copy startup scripts
COPY --from=builder --chown=nextjs:nodejs /app/scripts ./scripts

# Set proper permissions
RUN chown -R nextjs:nodejs /app

# Switch to non-root user
USER nextjs

# Expose ports
EXPOSE 3000 3001

# Health check
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD curl -f http://localhost:3001/health || exit 1

# Use dumb-init to handle signals properly
ENTRYPOINT ["dumb-init", "--"]

# Default command (can be overridden)
CMD ["npm", "start"]

# ================================
# Stage 5: Development Runtime
# ================================
FROM base AS development

# Install development tools
RUN apk add --no-cache \
    bash \
    vim \
    htop \
    && rm -rf /var/cache/apk/*

# Copy source code
COPY . .

# Install all dependencies
RUN npm ci

# Switch to non-root user
USER nextjs

# Expose ports for development
EXPOSE 3000 3001 5173

# Development command
CMD ["npm", "run", "dev"]

# ================================
# Stage 6: Testing Runtime
# ================================
FROM builder AS testing

# Install testing dependencies
RUN npm ci --include=dev

# Copy test files
COPY --chown=nextjs:nodejs ./tests ./tests
COPY --chown=nextjs:nodejs ./e2e ./e2e
COPY --chown=nextjs:nodejs ./*.config.js ./

# Switch to non-root user
USER nextjs

# Run tests
CMD ["npm", "test"]

# ================================
# Stage 7: Security Scanning
# ================================
FROM builder AS security

# Install security scanning tools
RUN npm install -g audit-ci retire

# Run security scans
RUN npm audit --audit-level=moderate
RUN retire --path ./node_modules

# Switch to non-root user
USER nextjs

CMD ["echo", "Security scan completed"]

# ================================
# Labels for metadata
# ================================
LABEL maintainer="Metamorphic Reactor Team"
LABEL version="1.0.0"
LABEL description="Metamorphic Reactor - AI-powered code transformation platform"
LABEL org.opencontainers.image.title="Metamorphic Reactor"
LABEL org.opencontainers.image.description="AI-powered code transformation platform with dual-agent architecture"
LABEL org.opencontainers.image.version="1.0.0"
LABEL org.opencontainers.image.vendor="Metamorphic Reactor"
LABEL org.opencontainers.image.licenses="MIT"
LABEL org.opencontainers.image.source="https://github.com/metamorphic-reactor/code-alchemy-reactor"

# Security labels
LABEL security.scan.enabled="true"
LABEL security.updates.enabled="true"
