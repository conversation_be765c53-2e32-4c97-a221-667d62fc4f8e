{"ci": {"collect": {"url": ["http://localhost:4173/", "http://localhost:4173/dashboard", "http://localhost:4173/monitoring"], "startServerCommand": "cd apps/web && npm run preview", "startServerReadyPattern": "Local:", "startServerReadyTimeout": 60000, "numberOfRuns": 3, "settings": {"preset": "desktop", "chromeFlags": "--no-sandbox --disable-dev-shm-usage", "emulatedFormFactor": "desktop", "throttling": {"rttMs": 40, "throughputKbps": 10240, "cpuSlowdownMultiplier": 1, "requestLatencyMs": 0, "downloadThroughputKbps": 0, "uploadThroughputKbps": 0}, "screenEmulation": {"mobile": false, "width": 1280, "height": 720, "deviceScaleFactor": 1, "disabled": false}}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.9}], "categories:accessibility": ["error", {"minScore": 0.97}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.8}], "audits:largest-contentful-paint": ["error", {"maxNumericValue": 3000}], "audits:max-potential-fid": ["error", {"maxNumericValue": 200}], "audits:cumulative-layout-shift": ["error", {"maxNumericValue": 0.05}], "audits:first-contentful-paint": ["warn", {"maxNumericValue": 1500}], "audits:speed-index": ["warn", {"maxNumericValue": 2500}], "audits:interactive": ["warn", {"maxNumericValue": 4000}], "audits:unused-javascript": ["warn", {"maxNumericValue": 100000}], "audits:unused-css-rules": ["warn", {"maxNumericValue": 50000}], "audits:render-blocking-resources": ["warn", {"maxNumericValue": 500}], "audits:unminified-javascript": ["error", {"maxNumericValue": 0}], "audits:unminified-css": ["error", {"maxNumericValue": 0}], "audits:total-byte-weight": ["warn", {"maxNumericValue": 1000000}], "audits:dom-size": ["warn", {"maxNumericValue": 1500}], "audits:color-contrast": ["error", {"minScore": 1}], "audits:heading-order": ["error", {"minScore": 1}], "audits:link-name": ["error", {"minScore": 1}], "audits:button-name": ["error", {"minScore": 1}], "audits:aria-allowed-attr": ["error", {"minScore": 1}], "audits:aria-required-attr": ["error", {"minScore": 1}], "audits:aria-valid-attr-value": ["error", {"minScore": 1}], "audits:aria-valid-attr": ["error", {"minScore": 1}], "audits:duplicate-id-aria": ["error", {"minScore": 1}], "audits:duplicate-id-active": ["error", {"minScore": 1}], "audits:focus-traps": ["error", {"minScore": 1}], "audits:focusable-controls": ["error", {"minScore": 1}], "audits:interactive-element-affordance": ["error", {"minScore": 1}], "audits:logical-tab-order": ["error", {"minScore": 1}], "audits:managed-focus": ["error", {"minScore": 1}], "audits:offscreen-content-hidden": ["error", {"minScore": 1}], "audits:use-landmarks": ["warn", {"minScore": 0.8}], "audits:valid-lang": ["error", {"minScore": 1}]}}, "upload": {"target": "temporary-public-storage", "outputDir": "./lhci_reports/desktop"}}}