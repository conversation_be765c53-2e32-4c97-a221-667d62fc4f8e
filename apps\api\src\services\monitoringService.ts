import { trace, metrics, SpanStatusCode, SpanKind } from '@opentelemetry/api';
import { NodeSDK } from '@opentelemetry/sdk-node';
import { OTLPTraceExporter } from '@opentelemetry/exporter-otlp-http';
import { HttpInstrumentation } from '@opentelemetry/instrumentation-http';
import { ExpressInstrumentation } from '@opentelemetry/instrumentation-express';

// Monitoring configuration
interface MonitoringConfig {
  serviceName: string;
  serviceVersion: string;
  environment: string;
  otlpEndpoint?: string;
  enableTracing: boolean;
  enableMetrics: boolean;
  sampleRate: number;
}

// System metrics
interface SystemMetrics {
  timestamp: number;
  cpu: {
    usage: number;
    loadAverage: number[];
  };
  memory: {
    used: number;
    total: number;
    percentage: number;
    heapUsed: number;
    heapTotal: number;
  };
  requests: {
    total: number;
    successful: number;
    failed: number;
    avgResponseTime: number;
  };
  ai: {
    totalRequests: number;
    totalTokens: number;
    totalCost: number;
    avgLatency: number;
    errorRate: number;
  };
  cache: {
    hitRate: number;
    totalRequests: number;
    avgResponseTime: number;
  };
}

// AI operation metrics
interface AIOperationMetrics {
  provider: string;
  model: string;
  operation: string;
  latency: number;
  tokenUsage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
  };
  cost: number;
  success: boolean;
  error?: string;
  timestamp: number;
}

export class MonitoringService {
  private config: MonitoringConfig;
  private sdk: NodeSDK | null = null;
  private tracer: any;
  private meter: any;
  private systemMetrics: SystemMetrics;
  private aiMetrics: AIOperationMetrics[] = [];
  private requestMetrics: { [key: string]: number } = {};

  constructor(config?: Partial<MonitoringConfig>) {
    this.config = {
      serviceName: 'metamorphic-reactor-api',
      serviceVersion: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development',
      otlpEndpoint: process.env.OTLP_ENDPOINT || 'http://localhost:4318/v1/traces',
      enableTracing: process.env.ENABLE_TRACING === 'true',
      enableMetrics: process.env.ENABLE_METRICS === 'true',
      sampleRate: parseFloat(process.env.TRACE_SAMPLE_RATE || '0.1'),
      ...config
    };

    this.systemMetrics = this.initializeSystemMetrics();
    this.initializeOpenTelemetry();
  }

  // Initialize OpenTelemetry
  private initializeOpenTelemetry(): void {
    if (!this.config.enableTracing && !this.config.enableMetrics) {
      console.log('[Monitoring] OpenTelemetry disabled');
      return;
    }

    try {
      this.sdk = new NodeSDK({
        serviceName: this.config.serviceName,
        serviceVersion: this.config.serviceVersion,
        traceExporter: new OTLPTraceExporter({
          url: this.config.otlpEndpoint,
        }),
        instrumentations: [
          new HttpInstrumentation({
            requestHook: (span, request) => {
              span.setAttributes({
                'service.name': this.config.serviceName,
                'service.version': this.config.serviceVersion,
                'environment': this.config.environment,
              });
            },
          }),
          new ExpressInstrumentation({
            requestHook: (span, info) => {
              span.setAttributes({
                'http.route': info.route,
              });
            },
          }),
        ],
      });

      this.sdk.start();
      
      this.tracer = trace.getTracer(this.config.serviceName, this.config.serviceVersion);
      this.meter = metrics.getMeter(this.config.serviceName, this.config.serviceVersion);

      console.log('[Monitoring] OpenTelemetry initialized successfully');
    } catch (error) {
      console.error('[Monitoring] Failed to initialize OpenTelemetry:', error);
    }
  }

  // Initialize system metrics
  private initializeSystemMetrics(): SystemMetrics {
    return {
      timestamp: Date.now(),
      cpu: { usage: 0, loadAverage: [] },
      memory: { used: 0, total: 0, percentage: 0, heapUsed: 0, heapTotal: 0 },
      requests: { total: 0, successful: 0, failed: 0, avgResponseTime: 0 },
      ai: { totalRequests: 0, totalTokens: 0, totalCost: 0, avgLatency: 0, errorRate: 0 },
      cache: { hitRate: 0, totalRequests: 0, avgResponseTime: 0 }
    };
  }

  // Track AI operation
  async trackAIOperation(
    provider: string,
    model: string,
    operation: string,
    fn: () => Promise<any>
  ): Promise<any> {
    const startTime = Date.now();
    const span = this.tracer?.startSpan(`ai.${operation}`, {
      kind: SpanKind.CLIENT,
      attributes: {
        'ai.provider': provider,
        'ai.model': model,
        'ai.operation': operation,
      },
    });

    try {
      const result = await fn();
      const latency = Date.now() - startTime;

      // Extract metrics from result
      const tokenUsage = result.usage || { inputTokens: 0, outputTokens: 0, totalTokens: 0 };
      const cost = result.cost || 0;

      // Record AI metrics
      this.recordAIMetrics({
        provider,
        model,
        operation,
        latency,
        tokenUsage,
        cost,
        success: true,
        timestamp: Date.now(),
      });

      span?.setStatus({ code: SpanStatusCode.OK });
      span?.setAttributes({
        'ai.tokens.input': tokenUsage.inputTokens,
        'ai.tokens.output': tokenUsage.outputTokens,
        'ai.tokens.total': tokenUsage.totalTokens,
        'ai.cost': cost,
        'ai.latency': latency,
      });

      return result;
    } catch (error) {
      const latency = Date.now() - startTime;
      
      this.recordAIMetrics({
        provider,
        model,
        operation,
        latency,
        tokenUsage: { inputTokens: 0, outputTokens: 0, totalTokens: 0 },
        cost: 0,
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        timestamp: Date.now(),
      });

      span?.setStatus({ 
        code: SpanStatusCode.ERROR, 
        message: error instanceof Error ? error.message : 'Unknown error' 
      });
      
      throw error;
    } finally {
      span?.end();
    }
  }

  // Record AI metrics
  private recordAIMetrics(metrics: AIOperationMetrics): void {
    this.aiMetrics.push(metrics);
    
    // Keep only last 1000 entries to prevent memory issues
    if (this.aiMetrics.length > 1000) {
      this.aiMetrics = this.aiMetrics.slice(-1000);
    }

    // Update aggregated metrics
    this.updateAIAggregates();
  }

  // Update AI aggregate metrics
  private updateAIAggregates(): void {
    const recent = this.aiMetrics.slice(-100); // Last 100 operations
    
    this.systemMetrics.ai = {
      totalRequests: this.aiMetrics.length,
      totalTokens: this.aiMetrics.reduce((sum, m) => sum + m.tokenUsage.totalTokens, 0),
      totalCost: this.aiMetrics.reduce((sum, m) => sum + m.cost, 0),
      avgLatency: recent.reduce((sum, m) => sum + m.latency, 0) / recent.length || 0,
      errorRate: (recent.filter(m => !m.success).length / recent.length) * 100 || 0,
    };
  }

  // Track HTTP request
  trackRequest(method: string, path: string, statusCode: number, responseTime: number): void {
    const key = `${method}:${path}`;
    this.requestMetrics[key] = (this.requestMetrics[key] || 0) + 1;

    // Update request metrics
    this.systemMetrics.requests.total++;
    if (statusCode < 400) {
      this.systemMetrics.requests.successful++;
    } else {
      this.systemMetrics.requests.failed++;
    }

    // Update average response time
    const total = this.systemMetrics.requests.total;
    this.systemMetrics.requests.avgResponseTime = 
      (this.systemMetrics.requests.avgResponseTime * (total - 1) + responseTime) / total;
  }

  // Update system metrics
  updateSystemMetrics(): void {
    const memUsage = process.memoryUsage();
    const cpuUsage = process.cpuUsage();
    
    this.systemMetrics = {
      ...this.systemMetrics,
      timestamp: Date.now(),
      cpu: {
        usage: (cpuUsage.user + cpuUsage.system) / 1000000, // Convert to seconds
        loadAverage: require('os').loadavg(),
      },
      memory: {
        used: memUsage.rss,
        total: require('os').totalmem(),
        percentage: (memUsage.rss / require('os').totalmem()) * 100,
        heapUsed: memUsage.heapUsed,
        heapTotal: memUsage.heapTotal,
      },
    };
  }

  // Get current metrics
  getMetrics(): SystemMetrics {
    this.updateSystemMetrics();
    return { ...this.systemMetrics };
  }

  // Get AI metrics summary
  getAIMetricsSummary(timeRange?: number): any {
    const cutoff = timeRange ? Date.now() - timeRange : 0;
    const filtered = this.aiMetrics.filter(m => m.timestamp > cutoff);
    
    const byProvider = filtered.reduce((acc, m) => {
      if (!acc[m.provider]) {
        acc[m.provider] = { requests: 0, tokens: 0, cost: 0, errors: 0 };
      }
      acc[m.provider].requests++;
      acc[m.provider].tokens += m.tokenUsage.totalTokens;
      acc[m.provider].cost += m.cost;
      if (!m.success) acc[m.provider].errors++;
      return acc;
    }, {} as any);

    return {
      total: filtered.length,
      byProvider,
      totalTokens: filtered.reduce((sum, m) => sum + m.tokenUsage.totalTokens, 0),
      totalCost: filtered.reduce((sum, m) => sum + m.cost, 0),
      errorRate: (filtered.filter(m => !m.success).length / filtered.length) * 100 || 0,
    };
  }

  // Health check
  async healthCheck(): Promise<{ status: string; metrics: SystemMetrics }> {
    this.updateSystemMetrics();
    
    const status = this.systemMetrics.memory.percentage > 90 || 
                   this.systemMetrics.ai.errorRate > 50 ? 'unhealthy' : 'healthy';
    
    return {
      status,
      metrics: this.systemMetrics,
    };
  }

  // Shutdown
  async shutdown(): Promise<void> {
    try {
      await this.sdk?.shutdown();
      console.log('[Monitoring] OpenTelemetry SDK shutdown complete');
    } catch (error) {
      console.error('[Monitoring] Error shutting down OpenTelemetry SDK:', error);
    }
  }
}

// Singleton instance
export const monitoringService = new MonitoringService();

// Start periodic metrics collection
setInterval(() => {
  monitoringService.updateSystemMetrics();
}, 30000); // Update every 30 seconds
