import request from 'supertest';
import express from 'express';
import { 
  generateTestJWT, 
  generateExpiredTestJWT,
  getTestServiceKey,
  createAuthHeader,
  setupTestEnvironment,
  testEndpointAuthentication,
  TEST_USERS
} from './auth-helpers';

// Mock all external services
jest.mock('../services/supabase.js');
jest.mock('../services/billingService.js');
jest.mock('../services/secretsService.js');
jest.mock('../services/loggingService.js');
jest.mock('../services/queueService.js');

// Mock Supabase client
jest.mock('@supabase/supabase-js', () => ({
  createClient: jest.fn(() => ({
    auth: {
      getUser: jest.fn()
    }
  }))
}));

// Setup test environment
setupTestEnvironment();

// Import routes after mocking
import onboardingRouter from '../routes/onboarding';
import billingRouter from '../routes/billing';
import settingsRouter from '../routes/settings';
import notificationsRouter from '../routes/notifications';

// Create test app
const app = express();
app.use(express.json());
app.use('/api', onboardingRouter);
app.use('/api', billingRouter);
app.use('/api', settingsRouter);
app.use('/api', notificationsRouter);

describe('API Integration Tests with Authentication', () => {
  let mockSupabaseClient: any;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Get the mocked Supabase client
    const { createClient } = require('@supabase/supabase-js');
    mockSupabaseClient = createClient();
  });

  describe('Onboarding API', () => {
    beforeEach(() => {
      // Mock successful auth response for regular user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.regular },
        error: null
      });
    });

    describe('POST /api/onboarding/progress', () => {
      const validRequest = {
        step: 'api_keys',
        completed: true
      };

      it('should accept valid request with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .post('/api/onboarding/progress')
          .set('Authorization', createAuthHeader(token))
          .send(validRequest)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'post',
        '/api/onboarding/progress',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken'],
        validRequest
      );
    });

    describe('POST /api/onboarding/api-keys', () => {
      const validRequest = {
        openai: 'sk-test-key',
        anthropic: 'ant-test-key'
      };

      it('should accept valid API keys with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .post('/api/onboarding/api-keys')
          .set('Authorization', createAuthHeader(token))
          .send(validRequest)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'post',
        '/api/onboarding/api-keys',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken'],
        validRequest
      );
    });

    describe('GET /api/onboarding/status', () => {
      it('should return onboarding status with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .get('/api/onboarding/status')
          .set('Authorization', createAuthHeader(token))
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeDefined();
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'get',
        '/api/onboarding/status',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken']
      );
    });
  });

  describe('Billing API', () => {
    beforeEach(() => {
      // Mock successful auth response for regular user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.regular },
        error: null
      });
    });

    describe('POST /api/billing/usage', () => {
      const validRequest = {
        tokens: 1000,
        cost: 0.02,
        provider: 'openai'
      };

      it('should record usage with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .post('/api/billing/usage')
          .set('Authorization', createAuthHeader(token))
          .send(validRequest)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'post',
        '/api/billing/usage',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken'],
        validRequest
      );
    });

    describe('GET /api/billing/usage', () => {
      it('should return usage data with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .get('/api/billing/usage')
          .set('Authorization', createAuthHeader(token))
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeDefined();
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'get',
        '/api/billing/usage',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken']
      );
    });

    describe('GET /api/billing/limits', () => {
      it('should return billing limits with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .get('/api/billing/limits')
          .set('Authorization', createAuthHeader(token))
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeDefined();
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'get',
        '/api/billing/limits',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken']
      );
    });
  });

  describe('Settings API', () => {
    beforeEach(() => {
      // Mock successful auth response for regular user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.regular },
        error: null
      });
    });

    describe('GET /api/settings', () => {
      it('should return user settings with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .get('/api/settings')
          .set('Authorization', createAuthHeader(token))
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeDefined();
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'get',
        '/api/settings',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken']
      );
    });

    describe('PUT /api/settings', () => {
      const validRequest = {
        theme: 'dark',
        notifications: true
      };

      it('should update settings with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .put('/api/settings')
          .set('Authorization', createAuthHeader(token))
          .send(validRequest)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'put',
        '/api/settings',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken'],
        validRequest
      );
    });

    describe('DELETE /api/settings/:key', () => {
      it('should delete setting with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .delete('/api/settings/theme')
          .set('Authorization', createAuthHeader(token))
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'delete',
        '/api/settings/theme',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken']
      );
    });
  });

  describe('Notifications API', () => {
    beforeEach(() => {
      // Mock successful auth response for regular user
      mockSupabaseClient.auth.getUser.mockResolvedValue({
        data: { user: TEST_USERS.regular },
        error: null
      });
    });

    describe('GET /api/notifications', () => {
      it('should return notifications with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .get('/api/notifications')
          .set('Authorization', createAuthHeader(token))
          .expect(200);

        expect(response.body.success).toBe(true);
        expect(response.body.data).toBeDefined();
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'get',
        '/api/notifications',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken']
      );
    });

    describe('POST /api/notifications', () => {
      const validRequest = {
        title: 'Test Notification',
        message: 'This is a test notification',
        type: 'info'
      };

      it('should create notification with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .post('/api/notifications')
          .set('Authorization', createAuthHeader(token))
          .send(validRequest)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'post',
        '/api/notifications',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken'],
        validRequest
      );
    });

    describe('PUT /api/notifications/:id', () => {
      const validRequest = {
        read: true
      };

      it('should update notification with authentication', async () => {
        const token = generateTestJWT(TEST_USERS.regular);

        const response = await request(app)
          .put('/api/notifications/123')
          .set('Authorization', createAuthHeader(token))
          .send(validRequest)
          .expect(200);

        expect(response.body.success).toBe(true);
      });

      // Test authentication scenarios
      await testEndpointAuthentication(
        request(app),
        'put',
        '/api/notifications/123',
        ['validToken', 'expiredToken', 'invalidToken', 'missingToken'],
        validRequest
      );
    });
  });
});
