import { Redis } from 'ioredis';
import crypto from 'crypto';

// Cache configuration
interface CacheConfig {
  ttl: number; // Time to live in seconds
  keyPrefix: string;
  enableMetrics: boolean;
}

// Cache metrics
interface CacheMetrics {
  hits: number;
  misses: number;
  sets: number;
  deletes: number;
  errors: number;
  totalRequests: number;
  hitRate: number;
  avgResponseTime: number;
}

// AI response cache entry
interface AIResponseCacheEntry {
  response: any;
  provider: string;
  model: string;
  timestamp: number;
  tokenUsage: {
    inputTokens: number;
    outputTokens: number;
    totalTokens: number;
    cost: number;
  };
}

export class CacheService {
  private redis: Redis;
  private metrics: CacheMetrics;
  private config: CacheConfig;

  constructor(redisUrl?: string, config?: Partial<CacheConfig>) {
    this.config = {
      ttl: 300, // 5 minutes default
      keyPrefix: 'cache:',
      enableMetrics: true,
      ...config
    };

    this.redis = new Redis(redisUrl || process.env.REDIS_URL || 'redis://localhost:6379', {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keyPrefix: this.config.keyPrefix,
    });

    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      totalRequests: 0,
      hitRate: 0,
      avgResponseTime: 0
    };

    this.redis.on('error', (error) => {
      console.error('[Cache] Redis connection error:', error);
      this.metrics.errors++;
    });

    this.redis.on('connect', () => {
      console.log('[Cache] Redis connected successfully');
    });
  }

  // Generate cache key for AI responses
  private generateAIKey(provider: string, model: string, prompt: string, context?: any): string {
    const contextStr = context ? JSON.stringify(context) : '';
    const hash = crypto
      .createHash('sha256')
      .update(`${provider}:${model}:${prompt}:${contextStr}`)
      .digest('hex')
      .substring(0, 16);
    
    return `ai:${provider}:${model}:${hash}`;
  }

  // Cache AI response
  async cacheAIResponse(
    provider: string,
    model: string,
    prompt: string,
    response: any,
    tokenUsage: AIResponseCacheEntry['tokenUsage'],
    context?: any,
    customTTL?: number
  ): Promise<void> {
    const startTime = Date.now();
    
    try {
      const key = this.generateAIKey(provider, model, prompt, context);
      const cacheEntry: AIResponseCacheEntry = {
        response,
        provider,
        model,
        timestamp: Date.now(),
        tokenUsage
      };

      const ttl = customTTL || this.config.ttl;
      await this.redis.setex(key, ttl, JSON.stringify(cacheEntry));
      
      this.metrics.sets++;
      this.updateMetrics(startTime);
      
      console.log(`[Cache] Cached AI response for ${provider}:${model} (TTL: ${ttl}s)`);
    } catch (error) {
      console.error('[Cache] Error caching AI response:', error);
      this.metrics.errors++;
    }
  }

  // Get cached AI response
  async getCachedAIResponse(
    provider: string,
    model: string,
    prompt: string,
    context?: any
  ): Promise<AIResponseCacheEntry | null> {
    const startTime = Date.now();
    
    try {
      const key = this.generateAIKey(provider, model, prompt, context);
      const cached = await this.redis.get(key);
      
      this.metrics.totalRequests++;
      
      if (cached) {
        this.metrics.hits++;
        this.updateMetrics(startTime);
        
        const entry: AIResponseCacheEntry = JSON.parse(cached);
        console.log(`[Cache] Cache HIT for ${provider}:${model}`);
        return entry;
      } else {
        this.metrics.misses++;
        this.updateMetrics(startTime);
        
        console.log(`[Cache] Cache MISS for ${provider}:${model}`);
        return null;
      }
    } catch (error) {
      console.error('[Cache] Error getting cached AI response:', error);
      this.metrics.errors++;
      this.metrics.misses++;
      return null;
    }
  }

  // Generic cache operations
  async set(key: string, value: any, ttl?: number): Promise<void> {
    const startTime = Date.now();
    
    try {
      const serialized = typeof value === 'string' ? value : JSON.stringify(value);
      const cacheTTL = ttl || this.config.ttl;
      
      await this.redis.setex(key, cacheTTL, serialized);
      this.metrics.sets++;
      this.updateMetrics(startTime);
    } catch (error) {
      console.error('[Cache] Error setting cache:', error);
      this.metrics.errors++;
    }
  }

  async get<T = any>(key: string): Promise<T | null> {
    const startTime = Date.now();
    
    try {
      const cached = await this.redis.get(key);
      this.metrics.totalRequests++;
      
      if (cached) {
        this.metrics.hits++;
        this.updateMetrics(startTime);
        
        try {
          return JSON.parse(cached);
        } catch {
          return cached as T;
        }
      } else {
        this.metrics.misses++;
        this.updateMetrics(startTime);
        return null;
      }
    } catch (error) {
      console.error('[Cache] Error getting cache:', error);
      this.metrics.errors++;
      this.metrics.misses++;
      return null;
    }
  }

  async delete(key: string): Promise<void> {
    try {
      await this.redis.del(key);
      this.metrics.deletes++;
    } catch (error) {
      console.error('[Cache] Error deleting cache:', error);
      this.metrics.errors++;
    }
  }

  // Cache invalidation patterns
  async invalidatePattern(pattern: string): Promise<number> {
    try {
      const keys = await this.redis.keys(pattern);
      if (keys.length > 0) {
        const deleted = await this.redis.del(...keys);
        this.metrics.deletes += deleted;
        console.log(`[Cache] Invalidated ${deleted} keys matching pattern: ${pattern}`);
        return deleted;
      }
      return 0;
    } catch (error) {
      console.error('[Cache] Error invalidating pattern:', error);
      this.metrics.errors++;
      return 0;
    }
  }

  // Invalidate all AI responses for a specific provider
  async invalidateProvider(provider: string): Promise<number> {
    return this.invalidatePattern(`ai:${provider}:*`);
  }

  // Invalidate all AI responses for a specific model
  async invalidateModel(provider: string, model: string): Promise<number> {
    return this.invalidatePattern(`ai:${provider}:${model}:*`);
  }

  // Update metrics
  private updateMetrics(startTime: number): void {
    if (!this.config.enableMetrics) return;
    
    const responseTime = Date.now() - startTime;
    this.metrics.avgResponseTime = 
      (this.metrics.avgResponseTime * (this.metrics.totalRequests - 1) + responseTime) / 
      this.metrics.totalRequests;
    
    this.metrics.hitRate = this.metrics.totalRequests > 0 
      ? (this.metrics.hits / this.metrics.totalRequests) * 100 
      : 0;
  }

  // Get cache metrics
  getMetrics(): CacheMetrics {
    return { ...this.metrics };
  }

  // Reset metrics
  resetMetrics(): void {
    this.metrics = {
      hits: 0,
      misses: 0,
      sets: 0,
      deletes: 0,
      errors: 0,
      totalRequests: 0,
      hitRate: 0,
      avgResponseTime: 0
    };
  }

  // Get cache info
  async getInfo(): Promise<any> {
    try {
      const info = await this.redis.info('memory');
      const keyspace = await this.redis.info('keyspace');
      const stats = await this.redis.info('stats');
      
      return {
        memory: info,
        keyspace: keyspace,
        stats: stats,
        metrics: this.getMetrics()
      };
    } catch (error) {
      console.error('[Cache] Error getting cache info:', error);
      return null;
    }
  }

  // Health check
  async healthCheck(): Promise<boolean> {
    try {
      const result = await this.redis.ping();
      return result === 'PONG';
    } catch (error) {
      console.error('[Cache] Health check failed:', error);
      return false;
    }
  }

  // Cleanup and disconnect
  async disconnect(): Promise<void> {
    try {
      await this.redis.quit();
      console.log('[Cache] Redis connection closed');
    } catch (error) {
      console.error('[Cache] Error closing Redis connection:', error);
    }
  }
}

// Singleton instance
export const cacheService = new CacheService();
