# Page snapshot

```yaml
- region "Notifications (F8)":
  - list
- banner:
  - button "Back"
  - heading "Metamorphic Reactor" [level=1]
  - text: Dashboard System Online
  - button "Learn":
    - text: Learn
    - img
  - button "Monitor":
    - text: Monitor
    - img
  - button "File":
    - text: File
    - img
  - button "🌙 Toggle theme Dark"
  - navigation "breadcrumb":
    - list:
      - listitem:
        - link "Home":
          - /url: /
      - listitem:
        - link "Dashboard" [disabled]
- img
- heading "Configuration Required" [level=3]
- button "Setup Wizard":
  - img
  - text: Setup Wizard
- button "Manual Setup"
- paragraph: Some services need configuration to work properly. Use the Setup Wizard for guided configuration or manual setup below.
- img
- text: Supabase
- img
- text: GitHub OAuth
- alert:
  - img
  - strong: "GitHub OAuth not configured:"
  - text: PR creation will be unavailable. Configure GitHub OAuth to enable automatic pull request creation.
- main:
  - heading "Reactor Prompt" [level=2]
  - button "Enhanced"
  - button "Run Loop"
  - region "Code input editor":
    - code:
      - button "Find References"
      - text: "|"
      - button "Find References"
      - text: "|"
      - button "Find References"
      - button "Find References"
      - text: "|"
      - button "Find References"
      - text: "|"
      - button "Find References"
      - textbox "Code input editor"
  - separator
  - heading "Patch Viewer" [level=2]
  - button "Connect GitHub"
  - paragraph: No changes available
  - paragraph: Run the reactor loop to see code transformations
  - separator
  - heading "Reactor Console" [level=2]
  - tablist:
    - tab "Stream" [selected]
    - tab "Logs"
  - heading "Real-time Stream" [level=3]
  - button "Start":
    - img
    - text: Start
  - button "Clear":
    - img
    - text: Clear
  - text: Ready to stream 0 events
  - img
  - paragraph: No stream events yet
  - paragraph: Start the reactor loop to see real-time updates
- alert
- alert
```