import Anthropic from '@anthropic-ai/sdk';

/**
 * Versioned tool definitions for Anthropic Claude
 */
export const ANTHROPIC_TOOL_VERSIONS = {
  // Web search tools
  WEB_SEARCH_20250305: 'web_search_20250305',
  WEB_SEARCH_20241022: 'web_search_20241022',
  
  // Text editor tools
  TEXT_EDITOR_20250124: 'text_editor_20250124',
  TEXT_EDITOR_20241022: 'text_editor_20241022',
  
  // Computer use tools
  COMPUTER_USE_20250124: 'computer_use_20250124',
  COMPUTER_USE_20241022: 'computer_use_20241022',
  
  // File system tools
  FILE_SYSTEM_20250124: 'file_system_20250124',
  
  // Code execution tools
  CODE_EXECUTION_20250124: 'code_execution_20250124',
} as const;

/**
 * Model compatibility matrix for tool versions
 */
export const MODEL_TOOL_COMPATIBILITY = {
  'claude-opus-4-20250514': {
    supportedVersions: [
      ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20250305,
      ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20250124,
      ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20250124,
      ANTHROPIC_TOOL_VERSIONS.FILE_SYSTEM_20250124,
      ANTHROPIC_TOOL_VERSIONS.CODE_EXECUTION_20250124,
    ],
    defaultVersions: {
      web_search: ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20250305,
      text_editor: ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20250124,
      computer_use: ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20250124,
    }
  },
  'claude-sonnet-4-20250514': {
    supportedVersions: [
      ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20250305,
      ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20250124,
      ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20250124,
      ANTHROPIC_TOOL_VERSIONS.FILE_SYSTEM_20250124,
      ANTHROPIC_TOOL_VERSIONS.CODE_EXECUTION_20250124,
    ],
    defaultVersions: {
      web_search: ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20250305,
      text_editor: ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20250124,
      computer_use: ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20250124,
    }
  },
  'claude-3-7-sonnet-20241022': {
    supportedVersions: [
      ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20241022,
      ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20241022,
      ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20241022,
    ],
    defaultVersions: {
      web_search: ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20241022,
      text_editor: ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20241022,
      computer_use: ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20241022,
    }
  },
  'claude-3-5-sonnet-20241022': {
    supportedVersions: [
      ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20241022,
      ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20241022,
      ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20241022,
    ],
    defaultVersions: {
      web_search: ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20241022,
      text_editor: ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20241022,
      computer_use: ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20241022,
    }
  },
  'claude-3-5-haiku-20241022': {
    supportedVersions: [
      ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20241022,
      ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20241022,
    ],
    defaultVersions: {
      web_search: ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20241022,
      text_editor: ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20241022,
    }
  },
} as const;

/**
 * Tool schema definitions by version
 */
export const TOOL_SCHEMAS = {
  [ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20250305]: {
    name: 'web_search_20250305',
    description: 'Search the web for current information using the latest search capabilities',
    input_schema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query'
        },
        max_results: {
          type: 'number',
          minimum: 1,
          maximum: 10,
          default: 5,
          description: 'Maximum number of results to return'
        },
        time_range: {
          type: 'string',
          enum: ['day', 'week', 'month', 'year', 'all'],
          default: 'all',
          description: 'Time range for search results'
        }
      },
      required: ['query']
    }
  },
  
  [ANTHROPIC_TOOL_VERSIONS.WEB_SEARCH_20241022]: {
    name: 'web_search_20241022',
    description: 'Search the web for current information',
    input_schema: {
      type: 'object',
      properties: {
        query: {
          type: 'string',
          description: 'Search query'
        },
        max_results: {
          type: 'number',
          minimum: 1,
          maximum: 5,
          default: 3,
          description: 'Maximum number of results to return'
        }
      },
      required: ['query']
    }
  },

  [ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20250124]: {
    name: 'text_editor_20250124',
    description: 'Edit text files with advanced capabilities',
    input_schema: {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          enum: ['view', 'create', 'str_replace', 'insert', 'undo'],
          description: 'Editor command to execute'
        },
        path: {
          type: 'string',
          description: 'File path'
        },
        file_text: {
          type: 'string',
          description: 'Content for create command'
        },
        old_str: {
          type: 'string',
          description: 'String to replace (for str_replace)'
        },
        new_str: {
          type: 'string',
          description: 'Replacement string (for str_replace)'
        },
        insert_line: {
          type: 'number',
          description: 'Line number for insertion (for insert)'
        },
        new_str_insert: {
          type: 'string',
          description: 'String to insert (for insert)'
        }
      },
      required: ['command', 'path']
    }
  },

  [ANTHROPIC_TOOL_VERSIONS.TEXT_EDITOR_20241022]: {
    name: 'text_editor_20241022',
    description: 'Edit text files',
    input_schema: {
      type: 'object',
      properties: {
        command: {
          type: 'string',
          enum: ['view', 'create', 'str_replace'],
          description: 'Editor command to execute'
        },
        path: {
          type: 'string',
          description: 'File path'
        },
        file_text: {
          type: 'string',
          description: 'Content for create command'
        },
        old_str: {
          type: 'string',
          description: 'String to replace (for str_replace)'
        },
        new_str: {
          type: 'string',
          description: 'Replacement string (for str_replace)'
        }
      },
      required: ['command', 'path']
    }
  },

  [ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20250124]: {
    name: 'computer_use_20250124',
    description: 'Control computer interface with enhanced capabilities',
    input_schema: {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['screenshot', 'click', 'type', 'key', 'scroll', 'drag'],
          description: 'Action to perform'
        },
        coordinate: {
          type: 'array',
          items: { type: 'number' },
          minItems: 2,
          maxItems: 2,
          description: 'X, Y coordinates for click/drag actions'
        },
        text: {
          type: 'string',
          description: 'Text to type'
        },
        key: {
          type: 'string',
          description: 'Key to press'
        },
        scroll_direction: {
          type: 'string',
          enum: ['up', 'down', 'left', 'right'],
          description: 'Scroll direction'
        },
        scroll_amount: {
          type: 'number',
          default: 3,
          description: 'Scroll amount'
        }
      },
      required: ['action']
    }
  },

  [ANTHROPIC_TOOL_VERSIONS.COMPUTER_USE_20241022]: {
    name: 'computer_use_20241022',
    description: 'Control computer interface',
    input_schema: {
      type: 'object',
      properties: {
        action: {
          type: 'string',
          enum: ['screenshot', 'click', 'type', 'key'],
          description: 'Action to perform'
        },
        coordinate: {
          type: 'array',
          items: { type: 'number' },
          minItems: 2,
          maxItems: 2,
          description: 'X, Y coordinates for click actions'
        },
        text: {
          type: 'string',
          description: 'Text to type'
        },
        key: {
          type: 'string',
          description: 'Key to press'
        }
      },
      required: ['action']
    }
  },
} as const;

/**
 * Tool version manager for Anthropic Claude
 */
export class AnthropicToolVersionManager {
  /**
   * Get compatible tool versions for a model
   */
  static getCompatibleVersions(model: string): string[] {
    const compatibility = MODEL_TOOL_COMPATIBILITY[model as keyof typeof MODEL_TOOL_COMPATIBILITY];
    return [...(compatibility?.supportedVersions || [])];
  }

  /**
   * Get default tool version for a model and tool type
   */
  static getDefaultVersion(model: string, toolType: string): string | undefined {
    const compatibility = MODEL_TOOL_COMPATIBILITY[model as keyof typeof MODEL_TOOL_COMPATIBILITY];
    return compatibility?.defaultVersions[toolType as keyof typeof compatibility.defaultVersions];
  }

  /**
   * Check if a tool version is compatible with a model
   */
  static isVersionCompatible(model: string, toolVersion: string): boolean {
    const compatibleVersions = this.getCompatibleVersions(model);
    return compatibleVersions.includes(toolVersion);
  }

  /**
   * Get tool schema by version
   */
  static getToolSchema(version: string): Anthropic.Tool | undefined {
    return TOOL_SCHEMAS[version as keyof typeof TOOL_SCHEMAS];
  }

  /**
   * Get all available tool schemas for a model
   */
  static getAvailableTools(model: string): Anthropic.Tool[] {
    const compatibleVersions = this.getCompatibleVersions(model);
    return compatibleVersions
      .map(version => this.getToolSchema(version))
      .filter((schema): schema is Anthropic.Tool => schema !== undefined);
  }

  /**
   * Upgrade tool version to latest compatible version
   */
  static upgradeToLatestVersion(model: string, currentVersion: string): string {
    const compatibleVersions = this.getCompatibleVersions(model);
    
    // Extract tool type from current version
    const toolType = currentVersion.split('_').slice(0, -1).join('_');
    
    // Find latest version for this tool type
    const latestVersion = compatibleVersions
      .filter(version => version.startsWith(toolType))
      .sort()
      .pop();
    
    return latestVersion || currentVersion;
  }

  /**
   * Migrate tool configuration to new model
   */
  static migrateToolsForModel(
    currentModel: string,
    targetModel: string,
    currentTools: string[]
  ): {
    compatible: string[];
    upgraded: string[];
    incompatible: string[];
  } {
    const targetCompatible = this.getCompatibleVersions(targetModel);
    const result = {
      compatible: [] as string[],
      upgraded: [] as string[],
      incompatible: [] as string[],
    };

    for (const tool of currentTools) {
      if (targetCompatible.includes(tool)) {
        result.compatible.push(tool);
      } else {
        const upgraded = this.upgradeToLatestVersion(targetModel, tool);
        if (upgraded !== tool && targetCompatible.includes(upgraded)) {
          result.upgraded.push(upgraded);
        } else {
          result.incompatible.push(tool);
        }
      }
    }

    return result;
  }

  /**
   * Get recommended tools for a specific use case
   */
  static getRecommendedTools(
    model: string,
    useCase: 'planning' | 'critique' | 'general'
  ): Anthropic.Tool[] {
    const compatibility = MODEL_TOOL_COMPATIBILITY[model as keyof typeof MODEL_TOOL_COMPATIBILITY];
    if (!compatibility) return [];

    const recommendations = {
      planning: ['web_search', 'text_editor', 'file_system'],
      critique: ['web_search'],
      general: ['web_search', 'text_editor'],
    };

    const toolTypes = recommendations[useCase] || recommendations.general;
    const tools: Anthropic.Tool[] = [];

    for (const toolType of toolTypes) {
      const version = compatibility.defaultVersions[toolType as keyof typeof compatibility.defaultVersions];
      if (version) {
        const schema = this.getToolSchema(version);
        if (schema) {
          tools.push(schema);
        }
      }
    }

    return tools;
  }

  /**
   * Validate tool configuration for model
   */
  static validateToolConfiguration(
    model: string,
    tools: string[]
  ): {
    valid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];
    const compatibleVersions = this.getCompatibleVersions(model);

    for (const tool of tools) {
      if (!compatibleVersions.includes(tool)) {
        const upgraded = this.upgradeToLatestVersion(model, tool);
        if (upgraded !== tool && compatibleVersions.includes(upgraded)) {
          warnings.push(`Tool ${tool} can be upgraded to ${upgraded} for model ${model}`);
        } else {
          errors.push(`Tool ${tool} is not compatible with model ${model}`);
        }
      }
    }

    return {
      valid: errors.length === 0,
      errors,
      warnings,
    };
  }
}
