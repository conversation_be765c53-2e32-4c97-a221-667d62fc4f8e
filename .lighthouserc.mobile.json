{"ci": {"collect": {"url": ["http://localhost:4173/", "http://localhost:4173/dashboard", "http://localhost:4173/monitoring"], "startServerCommand": "cd apps/web && npm run preview", "startServerReadyPattern": "Local:", "startServerReadyTimeout": 60000, "numberOfRuns": 3, "settings": {"preset": "mobile", "chromeFlags": "--no-sandbox --disable-dev-shm-usage", "emulatedFormFactor": "mobile", "throttling": {"rttMs": 150, "throughputKbps": 1638.4, "cpuSlowdownMultiplier": 4, "requestLatencyMs": 150, "downloadThroughputKbps": 1638.4, "uploadThroughputKbps": 750}, "screenEmulation": {"mobile": true, "width": 375, "height": 667, "deviceScaleFactor": 2, "disabled": false}}}, "assert": {"assertions": {"categories:performance": ["error", {"minScore": 0.85}], "categories:accessibility": ["error", {"minScore": 0.97}], "categories:best-practices": ["error", {"minScore": 0.9}], "categories:seo": ["warn", {"minScore": 0.8}], "audits:largest-contentful-paint": ["error", {"maxNumericValue": 4000}], "audits:max-potential-fid": ["error", {"maxNumericValue": 300}], "audits:cumulative-layout-shift": ["error", {"maxNumericValue": 0.05}], "audits:first-contentful-paint": ["warn", {"maxNumericValue": 2000}], "audits:speed-index": ["warn", {"maxNumericValue": 3500}], "audits:interactive": ["warn", {"maxNumericValue": 6000}], "audits:unused-javascript": ["warn", {"maxNumericValue": 150000}], "audits:unused-css-rules": ["warn", {"maxNumericValue": 75000}], "audits:render-blocking-resources": ["warn", {"maxNumericValue": 800}], "audits:unminified-javascript": ["error", {"maxNumericValue": 0}], "audits:unminified-css": ["error", {"maxNumericValue": 0}], "audits:total-byte-weight": ["warn", {"maxNumericValue": 1500000}], "audits:dom-size": ["warn", {"maxNumericValue": 1500}], "audits:tap-targets": ["error", {"minScore": 1}], "audits:color-contrast": ["error", {"minScore": 1}], "audits:heading-order": ["error", {"minScore": 1}], "audits:link-name": ["error", {"minScore": 1}], "audits:button-name": ["error", {"minScore": 1}], "audits:aria-allowed-attr": ["error", {"minScore": 1}], "audits:aria-required-attr": ["error", {"minScore": 1}], "audits:aria-valid-attr-value": ["error", {"minScore": 1}], "audits:aria-valid-attr": ["error", {"minScore": 1}], "audits:duplicate-id-aria": ["error", {"minScore": 1}], "audits:duplicate-id-active": ["error", {"minScore": 1}], "audits:focus-traps": ["error", {"minScore": 1}], "audits:focusable-controls": ["error", {"minScore": 1}], "audits:interactive-element-affordance": ["error", {"minScore": 1}], "audits:logical-tab-order": ["error", {"minScore": 1}], "audits:managed-focus": ["error", {"minScore": 1}], "audits:offscreen-content-hidden": ["error", {"minScore": 1}], "audits:use-landmarks": ["warn", {"minScore": 0.8}], "audits:valid-lang": ["error", {"minScore": 1}]}}, "upload": {"target": "temporary-public-storage", "outputDir": "./lhci_reports/mobile"}}}