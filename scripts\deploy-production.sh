#!/bin/bash
# Production Deployment Script for Metamorphic Reactor
# Comprehensive deployment with health checks, rollback, and monitoring

set -euo pipefail

# Configuration
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"
DEPLOYMENT_ENV="${DEPLOYMENT_ENV:-production}"
DOCKER_COMPOSE_FILE="${PROJECT_ROOT}/docker-compose.prod.yml"
BACKUP_DIR="${PROJECT_ROOT}/backups"
LOG_FILE="${PROJECT_ROOT}/logs/deployment-$(date +%Y%m%d-%H%M%S).log"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Logging function
log() {
    local level=$1
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    
    case $level in
        INFO)  echo -e "${GREEN}[INFO]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        WARN)  echo -e "${YELLOW}[WARN]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        ERROR) echo -e "${RED}[ERROR]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
        DEBUG) echo -e "${BLUE}[DEBUG]${NC} ${timestamp} - $message" | tee -a "$LOG_FILE" ;;
    esac
}

# Error handler
error_exit() {
    log ERROR "$1"
    exit 1
}

# Check prerequisites
check_prerequisites() {
    log INFO "Checking deployment prerequisites..."
    
    # Check if Docker is running
    if ! docker info >/dev/null 2>&1; then
        error_exit "Docker is not running or not accessible"
    fi
    
    # Check if docker-compose is available
    if ! command -v docker-compose >/dev/null 2>&1; then
        error_exit "docker-compose is not installed"
    fi
    
    # Check if required environment variables are set
    local required_vars=(
        "DOMAIN"
        "ACME_EMAIL"
        "SUPABASE_URL"
        "SUPABASE_SERVICE_ROLE_KEY"
        "OPENAI_API_KEY"
        "GRAFANA_PASSWORD"
    )
    
    for var in "${required_vars[@]}"; do
        if [[ -z "${!var:-}" ]]; then
            error_exit "Required environment variable $var is not set"
        fi
    done
    
    # Check if compose file exists
    if [[ ! -f "$DOCKER_COMPOSE_FILE" ]]; then
        error_exit "Docker compose file not found: $DOCKER_COMPOSE_FILE"
    fi
    
    log INFO "Prerequisites check passed"
}

# Create backup
create_backup() {
    log INFO "Creating backup before deployment..."
    
    mkdir -p "$BACKUP_DIR"
    local backup_name="backup-$(date +%Y%m%d-%H%M%S)"
    local backup_path="$BACKUP_DIR/$backup_name"
    
    # Backup Redis data
    if docker ps | grep -q metamorphic-redis; then
        log INFO "Backing up Redis data..."
        docker exec metamorphic-redis redis-cli BGSAVE
        sleep 5
        docker cp metamorphic-redis:/data/dump.rdb "$backup_path-redis.rdb"
    fi
    
    # Backup Grafana data
    if docker ps | grep -q metamorphic-grafana; then
        log INFO "Backing up Grafana data..."
        docker exec metamorphic-grafana tar czf /tmp/grafana-backup.tar.gz /var/lib/grafana
        docker cp metamorphic-grafana:/tmp/grafana-backup.tar.gz "$backup_path-grafana.tar.gz"
    fi
    
    # Backup configuration files
    log INFO "Backing up configuration files..."
    tar czf "$backup_path-config.tar.gz" \
        "$PROJECT_ROOT/config" \
        "$PROJECT_ROOT/.env" \
        "$PROJECT_ROOT/docker-compose.prod.yml" 2>/dev/null || true
    
    log INFO "Backup created: $backup_name"
    echo "$backup_name" > "$BACKUP_DIR/latest-backup.txt"
}

# Health check function
health_check() {
    local service=$1
    local url=$2
    local max_attempts=${3:-30}
    local wait_time=${4:-10}
    
    log INFO "Performing health check for $service..."
    
    for ((i=1; i<=max_attempts; i++)); do
        if curl -f -s "$url" >/dev/null 2>&1; then
            log INFO "$service health check passed"
            return 0
        fi
        
        log DEBUG "Health check attempt $i/$max_attempts failed, waiting ${wait_time}s..."
        sleep "$wait_time"
    done
    
    log ERROR "$service health check failed after $max_attempts attempts"
    return 1
}

# Deploy services
deploy_services() {
    log INFO "Starting deployment of services..."
    
    cd "$PROJECT_ROOT"
    
    # Pull latest images
    log INFO "Pulling latest Docker images..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" pull
    
    # Start infrastructure services first
    log INFO "Starting infrastructure services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d redis traefik prometheus grafana jaeger
    
    # Wait for infrastructure to be ready
    sleep 30
    
    # Start application services
    log INFO "Starting application services..."
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d api web
    
    # Wait for services to start
    sleep 60
    
    log INFO "Services deployment completed"
}

# Run health checks
run_health_checks() {
    log INFO "Running comprehensive health checks..."
    
    local domain="${DOMAIN:-localhost}"
    local protocol="https"
    
    # If using localhost, use http
    if [[ "$domain" == "localhost" ]]; then
        protocol="http"
    fi
    
    # Health check endpoints
    local endpoints=(
        "api:${protocol}://api.${domain}/health"
        "web:${protocol}://${domain}/health"
        "grafana:${protocol}://grafana.${domain}/api/health"
        "prometheus:${protocol}://prometheus.${domain}/-/healthy"
        "jaeger:${protocol}://jaeger.${domain}/"
    )
    
    local failed_checks=0
    
    for endpoint in "${endpoints[@]}"; do
        local service="${endpoint%%:*}"
        local url="${endpoint#*:}"
        
        if ! health_check "$service" "$url" 20 15; then
            ((failed_checks++))
        fi
    done
    
    if [[ $failed_checks -gt 0 ]]; then
        log ERROR "$failed_checks health checks failed"
        return 1
    fi
    
    log INFO "All health checks passed"
    return 0
}

# Run smoke tests
run_smoke_tests() {
    log INFO "Running smoke tests..."
    
    local domain="${DOMAIN:-localhost}"
    local protocol="https"
    
    if [[ "$domain" == "localhost" ]]; then
        protocol="http"
    fi
    
    # Basic API smoke test
    local api_response
    api_response=$(curl -s "${protocol}://api.${domain}/health" | jq -r '.status' 2>/dev/null || echo "error")
    
    if [[ "$api_response" != "ok" ]]; then
        log ERROR "API smoke test failed"
        return 1
    fi
    
    # Test monitoring endpoints
    if ! curl -f -s "${protocol}://api.${domain}/api/monitoring/metrics" >/dev/null; then
        log ERROR "Monitoring endpoint smoke test failed"
        return 1
    fi
    
    # Test rate limiting
    if ! curl -f -s "${protocol}://api.${domain}/api/rate-limit-status" >/dev/null; then
        log ERROR "Rate limiting endpoint smoke test failed"
        return 1
    fi
    
    log INFO "Smoke tests passed"
    return 0
}

# Rollback function
rollback() {
    log WARN "Initiating rollback..."
    
    # Stop current services
    docker-compose -f "$DOCKER_COMPOSE_FILE" down
    
    # Restore from backup if available
    if [[ -f "$BACKUP_DIR/latest-backup.txt" ]]; then
        local backup_name
        backup_name=$(cat "$BACKUP_DIR/latest-backup.txt")
        log INFO "Restoring from backup: $backup_name"
        
        # Restore Redis data
        if [[ -f "$BACKUP_DIR/$backup_name-redis.rdb" ]]; then
            docker run --rm -v redis-data:/data -v "$BACKUP_DIR":/backup alpine \
                cp /backup/"$backup_name"-redis.rdb /data/dump.rdb
        fi
        
        # Restore Grafana data
        if [[ -f "$BACKUP_DIR/$backup_name-grafana.tar.gz" ]]; then
            docker run --rm -v grafana-data:/data -v "$BACKUP_DIR":/backup alpine \
                tar xzf /backup/"$backup_name"-grafana.tar.gz -C /
        fi
    fi
    
    # Start services with previous configuration
    docker-compose -f "$DOCKER_COMPOSE_FILE" up -d
    
    log WARN "Rollback completed"
}

# Cleanup old backups
cleanup_backups() {
    log INFO "Cleaning up old backups..."
    
    # Keep only last 10 backups
    find "$BACKUP_DIR" -name "backup-*" -type f | sort -r | tail -n +11 | xargs rm -f
    
    log INFO "Backup cleanup completed"
}

# Main deployment function
main() {
    log INFO "Starting production deployment for Metamorphic Reactor"
    log INFO "Environment: $DEPLOYMENT_ENV"
    log INFO "Domain: ${DOMAIN:-localhost}"
    
    # Create log directory
    mkdir -p "$(dirname "$LOG_FILE")"
    
    # Trap to handle errors
    trap 'log ERROR "Deployment failed at line $LINENO"; rollback; exit 1' ERR
    
    # Run deployment steps
    check_prerequisites
    create_backup
    deploy_services
    
    # Run health checks with retries
    if ! run_health_checks; then
        log ERROR "Health checks failed, initiating rollback"
        rollback
        exit 1
    fi
    
    # Run smoke tests
    if ! run_smoke_tests; then
        log ERROR "Smoke tests failed, initiating rollback"
        rollback
        exit 1
    fi
    
    # Cleanup
    cleanup_backups
    
    log INFO "🚀 Production deployment completed successfully!"
    log INFO "Services are available at:"
    log INFO "  - Web: https://${DOMAIN:-localhost}"
    log INFO "  - API: https://api.${DOMAIN:-localhost}"
    log INFO "  - Monitoring: https://grafana.${DOMAIN:-localhost}"
    log INFO "  - Tracing: https://jaeger.${DOMAIN:-localhost}"
    log INFO "  - Metrics: https://prometheus.${DOMAIN:-localhost}"
    
    # Send success notification
    if command -v curl >/dev/null 2>&1 && [[ -n "${SLACK_WEBHOOK_URL:-}" ]]; then
        curl -X POST -H 'Content-type: application/json' \
            --data "{\"text\":\"🚀 Metamorphic Reactor deployed successfully to production!\"}" \
            "$SLACK_WEBHOOK_URL" >/dev/null 2>&1 || true
    fi
}

# Run main function
main "$@"
