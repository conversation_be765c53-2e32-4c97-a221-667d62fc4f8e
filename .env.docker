# Docker Compose Environment Variables
# Copy this file to .env and fill in your actual values

# =============================================================================
# SUPABASE CONFIGURATION
# =============================================================================
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_ANON_KEY=your-supabase-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# =============================================================================
# AI PROVIDER API KEYS
# =============================================================================
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key

# =============================================================================
# GITHUB OAUTH (Optional)
# =============================================================================
GITHUB_CLIENT_ID=your-github-oauth-client-id
GITHUB_CLIENT_SECRET=your-github-oauth-client-secret

# =============================================================================
# REDIS CONFIGURATION (Optional - uses container defaults)
# =============================================================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=

# =============================================================================
# APPLICATION CONFIGURATION
# =============================================================================
NODE_ENV=production
API_PORT=3001
WEB_PORT=8080

# =============================================================================
# SECURITY CONFIGURATION
# =============================================================================
JWT_SECRET=your-jwt-secret-key
CORS_ORIGIN=http://localhost:8080

# =============================================================================
# MONITORING (Optional)
# =============================================================================
LOG_LEVEL=info
ENABLE_METRICS=true
