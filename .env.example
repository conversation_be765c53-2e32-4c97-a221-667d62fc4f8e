# Metamorphic Reactor Environment Variables
# Copy this file to .env and fill in your actual values

# =============================================================================
# FRONTEND ENVIRONMENT VARIABLES (apps/web/.env)
# =============================================================================

# Supabase Configuration
VITE_SUPABASE_URL=https://your-project-id.supabase.co
VITE_SUPABASE_ANON_KEY=your-supabase-anon-key

# GitHub OAuth (for PR creation)
VITE_GITHUB_CLIENT_ID=your-github-oauth-client-id

# API Configuration
VITE_API_URL=http://localhost:3001

# =============================================================================
# BACKEND ENVIRONMENT VARIABLES (apps/api/.env)
# =============================================================================

# Server Configuration
PORT=3001
NODE_ENV=development

# Supabase Configuration
SUPABASE_URL=https://your-project-id.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-supabase-service-role-key

# AI API Keys (Phase 1 Multi-Provider Support)
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
GOOGLE_API_KEY=your-google-ai-studio-api-key

# AI Provider Configuration
AI_PRIMARY_PROVIDER=openai
AI_FALLBACK_PROVIDER=anthropic
AI_COST_LIMIT_USD=3.00

# GitHub OAuth
GITHUB_CLIENT_ID=your-github-oauth-client-id
GITHUB_CLIENT_SECRET=your-github-oauth-client-secret

# CORS Configuration
CORS_ORIGIN=http://localhost:5173

# =============================================================================
# SUPABASE EDGE FUNCTIONS ENVIRONMENT VARIABLES
# =============================================================================

# AI API Keys (for Edge Functions)
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
GOOGLE_API_KEY=your-google-ai-studio-api-key

# GitHub Integration
GITHUB_CLIENT_ID=your-github-oauth-client-id
GITHUB_CLIENT_SECRET=your-github-oauth-client-secret

# =============================================================================
# TESTING ENVIRONMENT VARIABLES
# =============================================================================

# Test Database (optional - uses main DB if not specified)
TEST_SUPABASE_URL=https://your-test-project.supabase.co
TEST_SUPABASE_ANON_KEY=your-test-supabase-anon-key

# Mock API Keys for Testing
TEST_OPENAI_API_KEY=sk-test-key
TEST_ANTHROPIC_API_KEY=sk-ant-test-key

# =============================================================================
# CI/CD ENVIRONMENT VARIABLES (GitHub Secrets)
# =============================================================================

# Supabase
SUPABASE_PROJECT_REF=your-project-ref
SUPABASE_ACCESS_TOKEN=your-supabase-access-token

# Deployment
VERCEL_TOKEN=your-vercel-token
VERCEL_ORG_ID=your-vercel-org-id
VERCEL_PROJECT_ID=your-vercel-project-id
RAILWAY_TOKEN=your-railway-token

# Code Quality
CODECOV_TOKEN=your-codecov-token
SNYK_TOKEN=your-snyk-token

# =============================================================================
# OPTIONAL ENVIRONMENT VARIABLES
# =============================================================================

# Telemetry (set to false to disable)
ENABLE_TELEMETRY=true

# Rate Limiting
MAX_REQUESTS_PER_MINUTE=60
MAX_COST_PER_USER_USD=10.00

# Logging
LOG_LEVEL=info
LOG_FORMAT=json

# Security
JWT_SECRET=your-jwt-secret-for-additional-auth
ENCRYPTION_KEY=your-encryption-key-for-sensitive-data

# =============================================================================
# SETUP INSTRUCTIONS
# =============================================================================

# 1. SUPABASE SETUP
#    - Create a new project at https://supabase.com
#    - Go to Settings > API to get your URL and keys
#    - Enable Row Level Security on all tables
#    - Deploy the Edge Functions using Supabase CLI

# 2. OPENAI SETUP
#    - Get API key from https://platform.openai.com/api-keys
#    - Ensure you have credits available
#    - GPT-4 access required for best results

# 3. ANTHROPIC SETUP (OPTIONAL)
#    - Get API key from https://console.anthropic.com
#    - Claude 3.5 Sonnet recommended for critique agent
#    - Fallback to GPT-4 if not available

# 4. GOOGLE AI SETUP (OPTIONAL)
#    - Get API key from https://aistudio.google.com/app/apikey
#    - Gemini 1.5 Ultra for multimodal capabilities
#    - Alternative provider for cost optimization

# 5. GITHUB OAUTH SETUP (OPTIONAL)
#    - Create OAuth app at https://github.com/settings/developers
#    - Set Authorization callback URL to: http://localhost:5173/auth/github/callback
#    - For production, add your production domain callback URL

# 6. DEPLOYMENT SETUP
#    - Vercel: Connect your GitHub repo for automatic deployments
#    - Railway: Connect for backend API deployment
#    - Supabase: Deploy Edge Functions for AI processing

# =============================================================================
# SECURITY NOTES
# =============================================================================

# - Never commit actual API keys to version control
# - Use different keys for development, staging, and production
# - Rotate keys regularly
# - Monitor API usage and costs
# - Enable rate limiting in production
# - Use HTTPS in production
# - Keep Supabase service role key secure (server-side only)

# =============================================================================
# COST MANAGEMENT
# =============================================================================

# Estimated costs (as of 2025):
# - OpenAI GPT-4o-mini: ~$0.00015 per 1K input tokens, ~$0.0006 per 1K output tokens
# - OpenAI GPT-4o: ~$0.0025 per 1K input tokens, ~$0.01 per 1K output tokens
# - Anthropic Claude 3.5 Sonnet: ~$0.003 per 1K input tokens, ~$0.015 per 1K output tokens
# - Google Gemini 1.5 Ultra: ~$0.00125 per 1K input tokens, ~$0.005 per 1K output tokens
# - Typical transformation: $0.005-0.05 (with GPT-4o-mini primary)
# - Set AI_COST_LIMIT_USD to control spending per operation

# =============================================================================
# TROUBLESHOOTING
# =============================================================================

# Common issues:
# 1. CORS errors: Check CORS_ORIGIN matches your frontend URL
# 2. Supabase connection: Verify URL and keys are correct
# 3. AI API errors: Check API keys and account credits
# 4. GitHub OAuth: Verify callback URLs match exactly
# 5. Edge Functions: Ensure they're deployed and environment variables are set
