import { Router, Request, Response } from 'express';
import { monitoringService } from '../services/monitoringService.js';
import { cacheService } from '../services/cacheService.js';
import { createAdvancedRateLimit } from '../middleware/advancedRateLimit.js';

const router = Router();

// Apply rate limiting to monitoring endpoints
router.use(createAdvancedRateLimit('general'));

// Get comprehensive system metrics
router.get('/metrics', async (req: Request, res: Response) => {
  try {
    const systemMetrics = monitoringService.getMetrics();
    const cacheMetrics = cacheService.getMetrics();
    const cacheInfo = await cacheService.getInfo();
    
    const timeRange = req.query.timeRange ? parseInt(req.query.timeRange as string) : undefined;
    const aiMetrics = monitoringService.getAIMetricsSummary(timeRange);

    res.json({
      timestamp: Date.now(),
      system: systemMetrics,
      cache: {
        metrics: cacheMetrics,
        info: cacheInfo
      },
      ai: aiMetrics,
      uptime: process.uptime(),
      version: process.env.npm_package_version || '1.0.0',
      environment: process.env.NODE_ENV || 'development'
    });
  } catch (error) {
    console.error('[Monitoring] Error getting metrics:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Health check endpoint with detailed status
router.get('/health', async (req: Request, res: Response) => {
  try {
    const healthCheck = await monitoringService.healthCheck();
    const cacheHealth = await cacheService.healthCheck();
    
    const overallStatus = healthCheck.status === 'healthy' && cacheHealth ? 'healthy' : 'unhealthy';
    
    const response = {
      status: overallStatus,
      timestamp: Date.now(),
      services: {
        api: healthCheck.status,
        cache: cacheHealth ? 'healthy' : 'unhealthy',
        monitoring: 'healthy'
      },
      metrics: healthCheck.metrics,
      uptime: process.uptime()
    };

    // Return appropriate HTTP status
    const statusCode = overallStatus === 'healthy' ? 200 : 503;
    res.status(statusCode).json(response);
  } catch (error) {
    console.error('[Monitoring] Health check error:', error);
    res.status(503).json({
      status: 'unhealthy',
      error: 'Health check failed',
      message: error instanceof Error ? error.message : 'Unknown error',
      timestamp: Date.now()
    });
  }
});

// Get AI metrics with filtering
router.get('/ai-metrics', async (req: Request, res: Response) => {
  try {
    const timeRange = req.query.timeRange ? parseInt(req.query.timeRange as string) : 3600000; // 1 hour default
    const provider = req.query.provider as string;
    const model = req.query.model as string;
    
    let aiMetrics = monitoringService.getAIMetricsSummary(timeRange);
    
    // Filter by provider if specified
    if (provider && aiMetrics.byProvider[provider]) {
      aiMetrics = {
        ...aiMetrics,
        byProvider: { [provider]: aiMetrics.byProvider[provider] }
      };
    }

    res.json({
      timeRange,
      filters: { provider, model },
      metrics: aiMetrics,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('[Monitoring] Error getting AI metrics:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve AI metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Get cache statistics
router.get('/cache-stats', async (req: Request, res: Response) => {
  try {
    const metrics = cacheService.getMetrics();
    const info = await cacheService.getInfo();
    const health = await cacheService.healthCheck();

    res.json({
      metrics,
      info,
      health,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('[Monitoring] Error getting cache stats:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve cache statistics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Cache management endpoints
router.post('/cache/invalidate', async (req: Request, res: Response) => {
  try {
    const { pattern, provider, model } = req.body;
    
    let deletedCount = 0;
    
    if (pattern) {
      deletedCount = await cacheService.invalidatePattern(pattern);
    } else if (provider && model) {
      deletedCount = await cacheService.invalidateModel(provider, model);
    } else if (provider) {
      deletedCount = await cacheService.invalidateProvider(provider);
    } else {
      return res.status(400).json({ 
        error: 'Invalid request',
        message: 'Must specify pattern, provider, or provider+model'
      });
    }

    res.json({
      success: true,
      deletedCount,
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('[Monitoring] Error invalidating cache:', error);
    res.status(500).json({ 
      error: 'Failed to invalidate cache',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Reset cache metrics
router.post('/cache/reset-metrics', async (req: Request, res: Response) => {
  try {
    cacheService.resetMetrics();
    res.json({
      success: true,
      message: 'Cache metrics reset successfully',
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('[Monitoring] Error resetting cache metrics:', error);
    res.status(500).json({ 
      error: 'Failed to reset cache metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// System information endpoint
router.get('/system-info', (req: Request, res: Response) => {
  try {
    const os = require('os');
    
    res.json({
      node: {
        version: process.version,
        platform: process.platform,
        arch: process.arch,
        uptime: process.uptime(),
        pid: process.pid
      },
      system: {
        hostname: os.hostname(),
        type: os.type(),
        release: os.release(),
        cpus: os.cpus().length,
        totalMemory: os.totalmem(),
        freeMemory: os.freemem(),
        loadAverage: os.loadavg()
      },
      environment: {
        nodeEnv: process.env.NODE_ENV,
        port: process.env.PORT,
        redisUrl: process.env.REDIS_URL ? '[CONFIGURED]' : '[NOT SET]',
        otlpEndpoint: process.env.OTLP_ENDPOINT ? '[CONFIGURED]' : '[NOT SET]'
      },
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('[Monitoring] Error getting system info:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve system information',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Performance metrics endpoint
router.get('/performance', async (req: Request, res: Response) => {
  try {
    const metrics = monitoringService.getMetrics();
    const cacheMetrics = cacheService.getMetrics();
    
    // Calculate performance scores
    const performanceScore = {
      overall: 0,
      api: {
        score: Math.max(0, 100 - (metrics.requests.avgResponseTime / 10)), // Penalize slow responses
        avgResponseTime: metrics.requests.avgResponseTime,
        errorRate: (metrics.requests.failed / metrics.requests.total) * 100 || 0
      },
      cache: {
        score: cacheMetrics.hitRate,
        hitRate: cacheMetrics.hitRate,
        avgResponseTime: cacheMetrics.avgResponseTime
      },
      ai: {
        score: Math.max(0, 100 - metrics.ai.errorRate),
        errorRate: metrics.ai.errorRate,
        avgLatency: metrics.ai.avgLatency,
        totalCost: metrics.ai.totalCost
      },
      system: {
        score: Math.max(0, 100 - metrics.memory.percentage),
        memoryUsage: metrics.memory.percentage,
        cpuUsage: metrics.cpu.usage
      }
    };

    // Calculate overall score
    performanceScore.overall = (
      performanceScore.api.score * 0.3 +
      performanceScore.cache.score * 0.2 +
      performanceScore.ai.score * 0.3 +
      performanceScore.system.score * 0.2
    );

    res.json({
      performance: performanceScore,
      recommendations: generateRecommendations(performanceScore),
      timestamp: Date.now()
    });
  } catch (error) {
    console.error('[Monitoring] Error getting performance metrics:', error);
    res.status(500).json({ 
      error: 'Failed to retrieve performance metrics',
      message: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

// Generate performance recommendations
function generateRecommendations(performance: any): string[] {
  const recommendations: string[] = [];
  
  if (performance.api.avgResponseTime > 1000) {
    recommendations.push('API response times are high. Consider optimizing database queries or adding caching.');
  }
  
  if (performance.cache.hitRate < 50) {
    recommendations.push('Cache hit rate is low. Review caching strategy and TTL settings.');
  }
  
  if (performance.ai.errorRate > 10) {
    recommendations.push('AI error rate is high. Check provider configurations and implement better error handling.');
  }
  
  if (performance.system.memoryUsage > 80) {
    recommendations.push('Memory usage is high. Consider scaling up or optimizing memory usage.');
  }
  
  if (performance.ai.totalCost > 100) {
    recommendations.push('AI costs are accumulating. Consider implementing cost controls or caching strategies.');
  }
  
  if (recommendations.length === 0) {
    recommendations.push('System is performing well. No immediate optimizations needed.');
  }
  
  return recommendations;
}

export { router as monitoringRouter };
