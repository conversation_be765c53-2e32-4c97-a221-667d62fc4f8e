# Production Deployment Guide

## Metamorphic Reactor - Production-Ready Deployment

This guide covers the complete production deployment of the Metamorphic Reactor platform with enterprise-grade features including monitoring, security, scalability, and observability.

## 🏗️ Architecture Overview

The production deployment includes:

- **Dual-Agent AI System**: Plan and Critique agents with provider failover
- **Advanced Rate Limiting**: Redis-backed sliding window rate limiting
- **Comprehensive Monitoring**: Prometheus, Grafana, Jaeger tracing
- **Intelligent Caching**: Multi-layer caching with AI response optimization
- **Security**: JWT authentication, CORS, security headers, input validation
- **Observability**: OpenTelemetry integration with distributed tracing
- **Auto-scaling**: Container orchestration with health checks
- **Cost Management**: Real-time cost tracking and budget controls

## 📋 Prerequisites

### System Requirements

- **OS**: Linux (Ubuntu 20.04+ recommended)
- **Memory**: 8GB RAM minimum, 16GB recommended
- **CPU**: 4 cores minimum, 8 cores recommended
- **Storage**: 100GB SSD minimum
- **Network**: Stable internet connection with static IP

### Software Dependencies

- Docker 24.0+
- Docker Compose 2.20+
- Git 2.30+
- curl, jq (for health checks)

### Required Accounts & API Keys

- **Supabase**: Database and authentication
- **OpenAI**: GPT-4 API access
- **Anthropic**: Claude API access (optional)
- **Google Cloud**: Vertex AI access (optional)
- **Domain**: SSL certificate (Let's Encrypt automatic)

## 🚀 Quick Start

### 1. Clone and Setup

```bash
git clone https://github.com/your-org/metamorphic-reactor.git
cd metamorphic-reactor
cp .env.production.template .env.production
```

### 2. Configure Environment

Edit `.env.production` with your actual values:

```bash
# Essential configuration
DOMAIN=your-domain.com
ACME_EMAIL=<EMAIL>
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key
OPENAI_API_KEY=sk-your-openai-api-key
GRAFANA_PASSWORD=your-secure-password
```

### 3. Deploy

```bash
# Make deployment script executable
chmod +x scripts/deploy-production.sh

# Run production deployment
./scripts/deploy-production.sh
```

### 4. Verify Deployment

The deployment script will automatically verify all services. You can also manually check:

```bash
# Check service status
docker-compose -f docker-compose.prod.yml ps

# Check health endpoints
curl https://your-domain.com/health
curl https://api.your-domain.com/health
```

## 🔧 Configuration Details

### Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `DOMAIN` | Your domain name | Yes | - |
| `ACME_EMAIL` | Email for SSL certificates | Yes | - |
| `SUPABASE_URL` | Supabase project URL | Yes | - |
| `SUPABASE_SERVICE_ROLE_KEY` | Supabase service key | Yes | - |
| `OPENAI_API_KEY` | OpenAI API key | Yes | - |
| `ANTHROPIC_API_KEY` | Anthropic API key | No | - |
| `GRAFANA_PASSWORD` | Grafana admin password | Yes | - |
| `ENABLE_TRACING` | Enable distributed tracing | No | true |
| `LOG_LEVEL` | Application log level | No | info |

### Service Configuration

#### Rate Limiting
- **General API**: 100 requests/5min, burst 20/min
- **AI Processing**: 20 requests/5min, burst 5/min
- **Authentication**: 10 requests/15min, burst 3/min
- **WebSocket**: 5 connections/min, burst 2/10sec

#### Caching
- **AI Responses**: 5 minutes TTL
- **General Cache**: 5 minutes TTL
- **Redis Memory**: 1GB with LRU eviction

#### Monitoring
- **Metrics Retention**: 30 days
- **Trace Retention**: 7 days
- **Log Retention**: 30 days

## 📊 Monitoring & Observability

### Access Monitoring Dashboards

- **Grafana**: https://grafana.your-domain.com
- **Prometheus**: https://prometheus.your-domain.com
- **Jaeger**: https://jaeger.your-domain.com
- **Traefik**: https://traefik.your-domain.com

### Key Metrics

#### Application Metrics
- Request rate and response times
- Error rates and status codes
- AI operation success rates
- Cost tracking and budget alerts

#### Infrastructure Metrics
- CPU and memory usage
- Network I/O and disk usage
- Container health and restarts
- Cache hit rates and performance

#### Business Metrics
- AI token usage and costs
- User activity and engagement
- Feature usage analytics
- Performance benchmarks

### Alerting

Alerts are configured for:
- High error rates (>10%)
- High response times (>2s)
- Low AI success rates (<90%)
- High memory usage (>90%)
- Service downtime
- High AI costs (>$10/hour)
- Low cache hit rates (<50%)

## 🔒 Security Features

### Authentication & Authorization
- JWT-based authentication with Supabase
- Role-based access control (RBAC)
- API key management and rotation
- Session management and timeout

### Network Security
- HTTPS enforcement with HSTS
- Security headers (CSP, X-Frame-Options, etc.)
- CORS configuration
- Rate limiting and DDoS protection

### Data Protection
- Input validation and sanitization
- Secret management and encryption
- Audit logging and compliance
- Data retention policies

### Container Security
- Non-root user execution
- Minimal base images (Alpine Linux)
- Security scanning in CI/CD
- Regular dependency updates

## 🔄 Backup & Recovery

### Automated Backups
- **Redis Data**: Daily snapshots
- **Grafana Dashboards**: Daily exports
- **Configuration**: Version controlled
- **Logs**: Centralized collection

### Recovery Procedures
```bash
# Restore from backup
./scripts/restore-backup.sh backup-20240101-120000

# Rollback deployment
./scripts/deploy-production.sh --rollback
```

## 📈 Scaling

### Horizontal Scaling
```bash
# Scale API instances
docker-compose -f docker-compose.prod.yml up -d --scale api=3

# Scale web instances
docker-compose -f docker-compose.prod.yml up -d --scale web=2
```

### Vertical Scaling
Update resource limits in `docker-compose.prod.yml`:
```yaml
deploy:
  resources:
    limits:
      memory: 2G
      cpus: '2.0'
```

## 🛠️ Maintenance

### Regular Tasks
- **Daily**: Check monitoring dashboards
- **Weekly**: Review logs and metrics
- **Monthly**: Update dependencies and security patches
- **Quarterly**: Performance optimization and capacity planning

### Health Checks
```bash
# Comprehensive health check
curl https://api.your-domain.com/api/monitoring/health

# Service-specific checks
curl https://api.your-domain.com/api/monitoring/metrics
curl https://api.your-domain.com/api/rate-limit-status
```

### Log Management
```bash
# View application logs
docker-compose -f docker-compose.prod.yml logs -f api

# View specific service logs
docker logs metamorphic-api --tail 100 -f
```

## 🚨 Troubleshooting

### Common Issues

#### Service Won't Start
1. Check environment variables
2. Verify Docker daemon is running
3. Check port availability
4. Review service logs

#### High Memory Usage
1. Check for memory leaks in logs
2. Review cache configuration
3. Scale services horizontally
4. Optimize AI request patterns

#### SSL Certificate Issues
1. Verify domain DNS configuration
2. Check Traefik logs
3. Ensure port 80/443 are accessible
4. Review Let's Encrypt rate limits

#### AI API Failures
1. Verify API keys are valid
2. Check rate limits and quotas
3. Review provider status pages
4. Enable failover providers

### Emergency Procedures

#### Complete System Failure
```bash
# Emergency rollback
./scripts/deploy-production.sh --emergency-rollback

# Restore from backup
./scripts/restore-backup.sh --latest --force
```

#### Security Incident
1. Immediately rotate all API keys
2. Review access logs
3. Update security configurations
4. Notify stakeholders

## 📞 Support

### Getting Help
- **Documentation**: Check this guide and inline comments
- **Logs**: Review application and service logs
- **Monitoring**: Check Grafana dashboards for insights
- **Community**: GitHub Issues and Discussions

### Performance Optimization
- Enable AI response caching
- Optimize database queries
- Use CDN for static assets
- Implement request batching
- Monitor and tune rate limits

## 🔄 Updates

### Updating the Application
```bash
# Pull latest changes
git pull origin main

# Rebuild and deploy
./scripts/deploy-production.sh --update
```

### Rolling Updates
The deployment script supports zero-downtime rolling updates with automatic health checks and rollback on failure.

---

## 📝 Additional Resources

- [API Documentation](./API.md)
- [Architecture Guide](./ARCHITECTURE.md)
- [Security Guide](./SECURITY.md)
- [Monitoring Guide](./MONITORING.md)
- [Development Guide](./DEVELOPMENT.md)
