import Anthropic from '@anthropic-ai/sdk';
import { 
  JSO<PERSON>atch, 
  PlanRequest, 
  CritiqueRequest, 
  CritiqueResult, 
  ProviderConfig, 
  ProviderResponse, 
  TokenUsage 
} from '../types.js';
import { AgentProvider, ProviderError, RateLimitError } from './AgentProvider.js';
import { registerProvider } from './ProviderFactory.js';

/**
 * Anthropic provider implementation using Claude 3.5 tool-use
 */
export class AnthropicProvider extends AgentProvider {
  private client: Anthropic;
  private readonly modelPricing: Record<string, { input: number; output: number }> = {
    'claude-opus-4-20250514': { input: 0.015, output: 0.075 },
    'claude-sonnet-4-20250514': { input: 0.003, output: 0.015 },
    'claude-3-7-sonnet-20241022': { input: 0.003, output: 0.015 },
    'claude-3-5-sonnet-20241022': { input: 0.003, output: 0.015 },
    'claude-3-5-haiku-20241022': { input: 0.00025, output: 0.00125 },
  };

  constructor(config: ProviderConfig) {
    super(config);
    
    const apiKey = config.apiKey || process.env.ANTHROPIC_API_KEY;
    if (!apiKey) {
      throw new ProviderError('Anthropic API key is required', 'anthropic', 'MISSING_API_KEY');
    }

    this.client = new Anthropic({
      apiKey,
      baseURL: config.anthropic?.baseURL,
      defaultHeaders: {
        'anthropic-version': config.anthropic?.version || '2023-06-01',
      },
    });
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test the configuration by making a simple API call
      await this.client.messages.create({
        model: this.config.model,
        max_tokens: 10,
        messages: [{ role: 'user', content: 'Hello' }],
      });
      return true;
    } catch (error) {
      console.error('Anthropic config validation failed:', error);
      return false;
    }
  }

  supportsStreaming(): boolean {
    return true;
  }

  async generatePatch(request: PlanRequest): Promise<ProviderResponse<JSONPatch>> {
    const startTime = Date.now();
    
    try {
      const systemPrompt = this.buildSystemPrompt('planner', this.config.systemPrompt);
      const userPrompt = this.buildPlanningPrompt(request);

      const response = await this.client.messages.create({
        model: this.config.model,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        system: systemPrompt,
        messages: [{ role: 'user', content: userPrompt }],
        tools: this.buildAnthropicTools('planner'),
      });

      const usage = this.createTokenUsage(response.usage);
      this.updateMetrics(usage);

      // Handle tool use response
      if (response.content.some(content => content.type === 'tool_use')) {
        const toolUse = response.content.find(content => content.type === 'tool_use') as any;
        if (toolUse?.name === 'generate_json_patch') {
          const patch = this.validateAndFormatPatch(toolUse.input);
          return {
            data: patch,
            usage,
            requestId: response.id,
            latency: Date.now() - startTime,
          };
        }
      }

      // Fallback to text parsing
      const textContent = response.content.find(content => content.type === 'text')?.text;
      if (!textContent) {
        throw new ProviderError('No content in Anthropic response', 'anthropic', 'EMPTY_RESPONSE');
      }

      let patch: JSONPatch;
      try {
        const parsed = JSON.parse(textContent);
        patch = this.validateAndFormatPatch(parsed);
      } catch (error) {
        throw new ProviderError(
          `Failed to parse Anthropic response as JSON: ${error instanceof Error ? error.message : 'Unknown error'}`,
          'anthropic',
          'INVALID_JSON'
        );
      }

      return {
        data: patch,
        usage,
        requestId: response.id,
        latency: Date.now() - startTime,
      };

    } catch (error) {
      if (error instanceof Anthropic.APIError) {
        if (error.status === 429) {
          throw new RateLimitError('anthropic', this.parseRetryAfter(error.headers || {}));
        }
        throw new ProviderError(
          `Anthropic API error: ${error.message}`,
          'anthropic',
          (error as any).type || 'API_ERROR',
          error.status
        );
      }
      throw error;
    }
  }

  async scorePatch(request: CritiqueRequest): Promise<ProviderResponse<CritiqueResult>> {
    const startTime = Date.now();
    
    try {
      const systemPrompt = this.buildSystemPrompt('critic', this.config.systemPrompt);
      const userPrompt = this.buildCritiquePrompt(request);

      const response = await this.client.messages.create({
        model: this.config.model,
        max_tokens: this.config.maxTokens,
        temperature: this.config.temperature,
        system: systemPrompt,
        messages: [{ role: 'user', content: userPrompt }],
        tools: this.buildAnthropicTools('critic'),
      });

      const usage = this.createTokenUsage(response.usage);
      this.updateMetrics(usage);

      // Handle tool use response
      if (response.content.some(content => content.type === 'tool_use')) {
        const toolUse = response.content.find(content => content.type === 'tool_use') as any;
        if (toolUse?.name === 'critique_json_patch') {
          const result = this.validateAndFormatCritique(toolUse.input);
          return {
            data: result,
            usage,
            requestId: response.id,
            latency: Date.now() - startTime,
          };
        }
      }

      // Fallback to text parsing
      const textContent = response.content.find(content => content.type === 'text')?.text;
      if (!textContent) {
        throw new ProviderError('No content in Anthropic response', 'anthropic', 'EMPTY_RESPONSE');
      }

      let result: CritiqueResult;
      try {
        const parsed = JSON.parse(textContent);
        result = this.validateAndFormatCritique(parsed);
      } catch (error) {
        throw new ProviderError(
          `Failed to parse Anthropic critique response: ${error instanceof Error ? error.message : 'Unknown error'}`,
          'anthropic',
          'INVALID_JSON'
        );
      }

      return {
        data: result,
        usage,
        requestId: response.id,
        latency: Date.now() - startTime,
      };

    } catch (error) {
      if (error instanceof Anthropic.APIError) {
        if (error.status === 429) {
          throw new RateLimitError('anthropic', this.parseRetryAfter(error.headers || {}));
        }
        throw new ProviderError(
          `Anthropic API error: ${error.message}`,
          'anthropic',
          (error as any).type || 'API_ERROR',
          error.status
        );
      }
      throw error;
    }
  }

  protected calculateCost(usage: { inputTokens: number; outputTokens: number }): number {
    const pricing = this.modelPricing[this.config.model];
    if (!pricing) {
      console.warn(`No pricing data for model ${this.config.model}, using default rates`);
      return (usage.inputTokens * 0.003 + usage.outputTokens * 0.015) / 1000;
    }
    
    return (usage.inputTokens * pricing.input + usage.outputTokens * pricing.output) / 1000;
  }

  private createTokenUsage(usage: Anthropic.Usage): TokenUsage {
    const inputTokens = usage.input_tokens || 0;
    const outputTokens = usage.output_tokens || 0;
    const totalTokens = inputTokens + outputTokens;
    
    return {
      inputTokens,
      outputTokens,
      totalTokens,
      cost: this.calculateCost({ inputTokens, outputTokens }),
      provider: 'anthropic',
      model: this.config.model,
      timestamp: new Date(),
    };
  }

  private buildPlanningPrompt(request: PlanRequest): string {
    let prompt = `Generate a JSON patch to implement the following request:\n\n`;
    prompt += `Request: ${request.prompt}\n\n`;
    
    if (request.context) {
      prompt += `Context: ${JSON.stringify(request.context, null, 2)}\n\n`;
    }
    
    if (request.previousAttempts && request.previousAttempts.length > 0) {
      prompt += `Previous attempts (learn from these):\n`;
      request.previousAttempts.forEach((attempt, index) => {
        prompt += `Attempt ${index + 1}: ${attempt.description}\n`;
      });
      prompt += '\n';
    }
    
    prompt += `Use the generate_json_patch tool to create the response.`;
    
    return prompt;
  }

  private buildCritiquePrompt(request: CritiqueRequest): string {
    let prompt = `Evaluate the following JSON patch:\n\n`;
    prompt += `Original Request: ${request.originalPrompt}\n\n`;
    prompt += `Proposed Patch: ${JSON.stringify(request.patch, null, 2)}\n\n`;
    
    if (request.context) {
      prompt += `Context: ${JSON.stringify(request.context, null, 2)}\n\n`;
    }
    
    prompt += `Evaluate on: correctness, completeness, quality, safety, and best practices.\n\n`;
    prompt += `Use the critique_json_patch tool to provide your evaluation.`;
    
    return prompt;
  }

  private buildAnthropicTools(role: 'planner' | 'critic'): Anthropic.Tool[] {
    if (role === 'planner') {
      return [
        {
          name: 'generate_json_patch',
          description: 'Generate a JSON patch to implement requested changes',
          input_schema: {
            type: 'object',
            properties: {
              operations: {
                type: 'array',
                description: 'Array of JSON Patch operations',
                items: {
                  type: 'object',
                  properties: {
                    op: { type: 'string', enum: ['add', 'remove', 'replace', 'move', 'copy', 'test'] },
                    path: { type: 'string' },
                    value: {},
                    from: { type: 'string' }
                  },
                  required: ['op', 'path']
                }
              },
              description: { type: 'string' },
              confidence: { type: 'number', minimum: 0, maximum: 1 }
            },
            required: ['operations', 'description', 'confidence']
          }
        },
        {
          name: 'web_search_20250305',
          description: 'Search the web for current information',
          input_schema: {
            type: 'object',
            properties: {
              query: { type: 'string' }
            },
            required: ['query']
          }
        }
      ];
    } else {
      return [
        {
          name: 'critique_json_patch',
          description: 'Evaluate and score a JSON patch for correctness and quality',
          input_schema: {
            type: 'object',
            properties: {
              score: { type: 'number', minimum: 0, maximum: 1 },
              feedback: { type: 'string' },
              suggestions: { type: 'array', items: { type: 'string' } },
              isAcceptable: { type: 'boolean' }
            },
            required: ['score', 'feedback', 'suggestions', 'isAcceptable']
          }
        }
      ];
    }
  }

  private validateAndFormatPatch(parsed: any): JSONPatch {
    if (!parsed.operations || !Array.isArray(parsed.operations)) {
      throw new Error('Missing or invalid operations array');
    }
    
    if (!parsed.description || typeof parsed.description !== 'string') {
      throw new Error('Missing or invalid description');
    }
    
    if (typeof parsed.confidence !== 'number' || parsed.confidence < 0 || parsed.confidence > 1) {
      throw new Error('Missing or invalid confidence score');
    }
    
    return {
      operations: parsed.operations,
      description: parsed.description,
      confidence: parsed.confidence,
    };
  }

  private validateAndFormatCritique(parsed: any): CritiqueResult {
    if (typeof parsed.score !== 'number' || parsed.score < 0 || parsed.score > 1) {
      throw new Error('Missing or invalid score');
    }
    
    if (!parsed.feedback || typeof parsed.feedback !== 'string') {
      throw new Error('Missing or invalid feedback');
    }
    
    if (!Array.isArray(parsed.suggestions)) {
      throw new Error('Missing or invalid suggestions array');
    }
    
    return {
      score: parsed.score,
      feedback: parsed.feedback,
      suggestions: parsed.suggestions,
      isAcceptable: parsed.isAcceptable ?? parsed.score >= 0.95,
    };
  }

  private parseRetryAfter(headers: Record<string, string>): number | undefined {
    const retryAfter = headers['retry-after'];
    return retryAfter ? parseInt(retryAfter, 10) * 1000 : undefined;
  }
}

// Auto-register the provider
registerProvider('anthropic', AnthropicProvider);
