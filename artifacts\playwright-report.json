{"config": {"configFile": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\playwright.config.ts", "rootDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": null, "globalTeardown": null, "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "preserveOutput": "always", "reporter": [["html", null], ["json", {"outputFile": "artifacts/playwright-report.json"}], ["junit", {"outputFile": "artifacts/playwright-results.xml"}]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "chromium", "name": "chromium", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "firefox", "name": "firefox", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "webkit", "name": "webkit", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/artifacts/test-results", "repeatEach": 1, "retries": 0, "metadata": {"test-suite": "Dashboard UI Controls", "target-accessibility-score": "97+", "browsers": "Chromium, Firefox, WebKit", "responsive-breakpoints": "320px, 768px, 1280px", "actualWorkers": 4}, "id": "Tablet", "name": "Tablet", "testDir": "C:/Users/<USER>/Desktop/metamorphic_reactor/code-alchemy-reactor/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.53.1", "workers": 4, "webServer": {"command": "npm run dev", "url": "http://localhost:8080", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "accessibility-audit.spec.ts", "file": "accessibility-audit.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Accessibility Audit", "file": "accessibility-audit.spec.ts", "line": 11, "column": 6, "specs": [{"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "failed", "duration": 23540, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  48 |\n  49 |     // Expect no critical or serious violations\n> 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);\n     |                                                                                    ^\n  51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);\n  52 |     \n  53 |     // Target score ≥ 97\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. aria-valid-attr-value: Ensure all ARIA attributes have valid values\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: ARIA attributes must conform to valid values\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 7\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:02:32.584Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-72eff16dad7ca79bd348", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 24981, "error": {"message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "snippet": "\u001b[0m \u001b[90m 66 |\u001b[39m     \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 67 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 68 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no critical violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 69 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no serious violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 71 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  67 |     \n> 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);\n     |                                                                                     ^\n  69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);\n  70 |   });\n  71 |\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:02:32.690Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-b013dbc9792d11479ce6", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 8, "parallelIndex": 2, "status": "timedOut", "duration": 37952, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 38, "line": 20}, "message": "Error: page.evaluate: Test timeout of 30000ms exceeded.\n Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md\n\n  18 |   test('should pass axe accessibility audit on main dashboard', async ({ page }) => {\n  19 |     // Run axe accessibility scan\n> 20 |     const accessibilityScanResults = await new AxeBuilder({ page })\n     |                                      ^\n  21 |       .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])\n  22 |       .analyze();\n  23 |\n    at AxeBuilder.analyze (C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\node_modules\\.pnpm\\@axe-core+playwright@4.10.2_playwright-core@1.53.1\\node_modules\\@axe-core\\playwright\\dist\\index.mjs:218:13)\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:20:38"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:24.884Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-64f0146ac4ebba4f324a", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 9, "parallelIndex": 0, "status": "timedOut", "duration": 44029, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"message": "Error: browserContext._wrapApiCall: Protocol error (Browser.removeBrowserContext): this._windows[aWindow.__SSi] is undefined"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 38, "line": 61}, "message": "Error: browserContext.newPage: Test timeout of 30000ms exceeded.\n Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md\n\n  59 |     await expect(page.getByRole('main')).toBeVisible();\n  60 |\n> 61 |     const accessibilityScanResults = await new AxeBuilder({ page })\n     |                                      ^\n  62 |       .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])\n  63 |       .analyze();\n  64 |\n    at AxeBuilder.analyze (C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\node_modules\\.pnpm\\@axe-core+playwright@4.10.2_playwright-core@1.53.1\\node_modules\\@axe-core\\playwright\\dist\\index.mjs:218:13)\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:61:38"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:26.192Z", "annotations": [], "attachments": []}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-0f948fa3623f5bb0e8c5", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 16, "parallelIndex": 2, "status": "failed", "duration": 22408, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  48 |\n  49 |     // Expect no critical or serious violations\n> 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);\n     |                                                                                    ^\n  51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);\n  52 |     \n  53 |     // Target score ≥ 97\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. aria-valid-attr-value: Ensure all ARIA attributes have valid values\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: ARIA attributes must conform to valid values\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 7\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:25.976Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-6f4382caf52c8a0dffc0", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 17, "parallelIndex": 0, "status": "failed", "duration": 26034, "error": {"message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "snippet": "\u001b[0m \u001b[90m 66 |\u001b[39m     \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 67 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 68 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no critical violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 69 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no serious violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 71 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  67 |     \n> 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);\n     |                                                                                     ^\n  69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);\n  70 |   });\n  71 |\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:25.981Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\\video.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\\video-1.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-b614128e75adea91a8a2", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 24, "parallelIndex": 0, "status": "failed", "duration": 7658, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  48 |\n  49 |     // Expect no critical or serious violations\n> 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);\n     |                                                                                    ^\n  51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);\n  52 |     \n  53 |     // Target score ≥ 97\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. button-name: Ensure buttons have discernible text\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: Buttons must have discernible text\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 1\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:18.156Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-dd1b2834653f11d8d38f", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 25, "parallelIndex": 3, "status": "failed", "duration": 6424, "error": {"message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "snippet": "\u001b[0m \u001b[90m 66 |\u001b[39m     \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 67 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 68 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no critical violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 69 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no serious violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 71 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  67 |     \n> 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);\n     |                                                                                     ^\n  69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);\n  70 |   });\n  71 |\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:28.021Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-e89eef7ca22fb0378a2d", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 31, "parallelIndex": 2, "status": "failed", "duration": 15543, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  48 |\n  49 |     // Expect no critical or serious violations\n> 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);\n     |                                                                                    ^\n  51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);\n  52 |     \n  53 |     // Target score ≥ 97\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. button-name: Ensure buttons have discernible text\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: Buttons must have discernible text\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 1\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:04.558Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-9825d36cf80d5b3420bc", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 32, "parallelIndex": 3, "status": "failed", "duration": 18969, "error": {"message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "snippet": "\u001b[0m \u001b[90m 66 |\u001b[39m     \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 67 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 68 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no critical violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 69 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no serious violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 71 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  67 |     \n> 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);\n     |                                                                                     ^\n  69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);\n  70 |   });\n  71 |\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:05.427Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-a1744cd3be04261ccfd1", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}, {"title": "should pass axe accessibility audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 38, "parallelIndex": 0, "status": "failed", "duration": 8787, "error": {"message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "snippet": "\u001b[0m \u001b[90m 48 |\u001b[39m\n \u001b[90m 49 |\u001b[39m     \u001b[90m// Expect no critical or serious violations\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 50 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no critical accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                    \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 51 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Should have no serious accessibility violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 52 |\u001b[39m     \n \u001b[90m 53 |\u001b[39m     \u001b[90m// Target score ≥ 97\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}, "message": "Error: Should have no critical accessibility violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  48 |\n  49 |     // Expect no critical or serious violations\n> 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);\n     |                                                                                    ^\n  51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);\n  52 |     \n  53 |     // Target score ≥ 97\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:50:84"}], "stdout": [{"text": "Accessibility violations: \u001b[33m2\u001b[39m\n"}, {"text": "Violations details:\n"}, {"text": "1. aria-valid-attr-value: Ensure all ARIA attributes have valid values\n"}, {"text": "   Impact: critical\n"}, {"text": "   Help: ARIA attributes must conform to valid values\n"}, {"text": "   Nodes: 2\n"}, {"text": "2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "   Impact: serious\n"}, {"text": "   Help: Elements must meet minimum color contrast ratio thresholds\n"}, {"text": "   Nodes: 7\n"}, {"text": "Accessibility Score: 85/100\n"}, {"text": "Critical: 1, Serious: 1, Moderate: 0, Minor: 0\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:50.505Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\video.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\video-1.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 84, "line": 50}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-cbdd7d906870c899785e", "file": "accessibility-audit.spec.ts", "line": 18, "column": 3}, {"title": "should pass axe audit on main dashboard", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 39, "parallelIndex": 2, "status": "failed", "duration": 9011, "error": {"message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "snippet": "\u001b[0m \u001b[90m 66 |\u001b[39m     \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 67 |\u001b[39m     \n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 68 |\u001b[39m     expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no critical violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 69 |\u001b[39m     expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m'Main dashboard should have no serious violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 70 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 71 |\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}, "message": "Error: Main dashboard should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  67 |     \n> 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);\n     |                                                                                     ^\n  69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);\n  70 |   });\n  71 |\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:68:85"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:08:02.304Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 68}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-0c7276d45176eb636fae", "file": "accessibility-audit.spec.ts", "line": 57, "column": 3}], "suites": [{"title": "Responsive Design Tests", "file": "accessibility-audit.spec.ts", "line": 72, "column": 8, "specs": [{"title": "should be accessible and functional at Mobile (320x568)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 24676, "error": {"message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:02:32.614Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-6ff64041a4179b495e20", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "failed", "duration": 24227, "error": {"message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:02:32.646Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-1a3e166534be339a02f4", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 0, "status": "failed", "duration": 14639, "error": {"message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:03.916Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-70e29fd4688ba8c6abbf", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 10, "parallelIndex": 1, "status": "timedOut", "duration": 37396, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "snippet": "\u001b[0m \u001b[90m 10 |\u001b[39m\n \u001b[90m 11 |\u001b[39m test\u001b[33m.\u001b[39mdescribe(\u001b[32m'Accessibility Audit'\u001b[39m\u001b[33m,\u001b[39m () \u001b[33m=>\u001b[39m {\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 12 |\u001b[39m   test\u001b[33m.\u001b[39mbeforeEach(\u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\n \u001b[90m    |\u001b[39m        \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 13 |\u001b[39m     \u001b[90m// Navigate to main dashboard\u001b[39m\n \u001b[90m 14 |\u001b[39m     \u001b[36mawait\u001b[39m page\u001b[33m.\u001b[39mgoto(\u001b[32m'http://localhost:8080/dashboard'\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 15 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mgetByRole(\u001b[32m'main'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}, "message": "\u001b[31mTest timeout of 30000ms exceeded while running \"beforeEach\" hook.\u001b[39m\n\n  10 |\n  11 | test.describe('Accessibility Audit', () => {\n> 12 |   test.beforeEach(async ({ page }) => {\n     |        ^\n  13 |     // Navigate to main dashboard\n  14 |     await page.goto('http://localhost:8080/dashboard');\n  15 |     await expect(page.getByRole('main')).toBeVisible();\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:12:8"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:27.060Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 8, "line": 12}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-aa202c5b44b99a94c53f", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 11, "parallelIndex": 3, "status": "timedOut", "duration": 39837, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 20, "line": 85}, "message": "Error: page.goto: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:8080/dashboard\", waiting until \"load\"\u001b[22m\n\n\n  83 |\n  84 |         // Navigate to main dashboard\n> 85 |         await page.goto('http://localhost:8080/dashboard');\n     |                    ^\n  86 |         await expect(page.getByRole('main')).toBeVisible();\n  87 |\n  88 |         // Take screenshot for visual verification\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:85:20"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:28.500Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-e43673ab422129bc4e02", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 12, "parallelIndex": 0, "status": "timedOut", "duration": 38703, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 42, "line": 95}, "message": "Error: page.evaluate: Test timeout of 30000ms exceeded.\n Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md\n\n  93 |\n  94 |         // Run accessibility audit at this viewport\n> 95 |         const accessibilityScanResults = await new AxeBuilder({ page })\n     |                                          ^\n  96 |           .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])\n  97 |           .analyze();\n  98 |\n    at AxeBuilder.analyze (C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\node_modules\\.pnpm\\@axe-core+playwright@4.10.2_playwright-core@1.53.1\\node_modules\\@axe-core\\playwright\\dist\\index.mjs:218:13)\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:95:42"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:04:35.452Z", "annotations": [], "attachments": [{"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-2e6718374c5409719943", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 18, "parallelIndex": 3, "status": "failed", "duration": 21407, "error": {"message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:26.198Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-12f93b8e4e9b39bf3e96", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 19, "parallelIndex": 1, "status": "failed", "duration": 26500, "error": {"message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:27.440Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-0ed426c583c4e254edff", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 20, "parallelIndex": 3, "status": "failed", "duration": 22282, "error": {"message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:56.420Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\\video.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\\video-1.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-b04b11248624e7d1770b", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 26, "parallelIndex": 1, "status": "failed", "duration": 9968, "error": {"message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:30.700Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-ebbcc92fc1005986139b", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 27, "parallelIndex": 2, "status": "failed", "duration": 12494, "error": {"message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:36.450Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-68cfe31063b3d02bc91f", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 28, "parallelIndex": 3, "status": "failed", "duration": 16548, "error": {"message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:43.015Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-378c49c48dbbe11bd1f0", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 33, "parallelIndex": 1, "status": "failed", "duration": 17070, "error": {"message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:08.107Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-30b41b8dbd8fad859861", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 34, "parallelIndex": 0, "status": "failed", "duration": 24163, "error": {"message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:17.422Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-ee8f952f48515738b273", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 35, "parallelIndex": 2, "status": "failed", "duration": 27620, "error": {"message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:27.483Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-58099b26a6dccd0ab994", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Mobile (320x568)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 40, "parallelIndex": 3, "status": "failed", "duration": 13466, "error": {"message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Mobile viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:08:05.840Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-4e0d889eabee0ce955a5", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Tablet (768x1024)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 41, "parallelIndex": 1, "status": "failed", "duration": 13050, "error": {"message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Tablet viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:08:09.111Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-ff65dcd2b9eef6f7b268", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}, {"title": "should be accessible and functional at Desktop (1280x720)", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 42, "parallelIndex": 0, "status": "failed", "duration": 10818, "error": {"message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "snippet": "\u001b[0m \u001b[90m 100 |\u001b[39m         \u001b[36mconst\u001b[39m seriousViolations \u001b[33m=\u001b[39m accessibilityScanResults\u001b[33m.\u001b[39mviolations\u001b[33m.\u001b[39mfilter(v \u001b[33m=>\u001b[39m v\u001b[33m.\u001b[39mimpact \u001b[33m===\u001b[39m \u001b[32m'serious'\u001b[39m)\u001b[33m.\u001b[39mlength\u001b[33m;\u001b[39m\n \u001b[90m 101 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 102 |\u001b[39m         expect(criticalViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no critical violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                           \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 103 |\u001b[39m         expect(seriousViolations\u001b[33m,\u001b[39m \u001b[32m`${name} viewport should have no serious violations`\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m 104 |\u001b[39m\n \u001b[90m 105 |\u001b[39m         \u001b[90m// Test key interactive elements are accessible\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}, "message": "Error: Desktop viewport should have no critical violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;\n  101 |\n> 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);\n      |                                                                                           ^\n  103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);\n  104 |\n  105 |         // Test key interactive elements are accessible\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:102:91"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:08:08.867Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 91, "line": 102}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-234d5085f62b2c8ae61f", "file": "accessibility-audit.spec.ts", "line": 80, "column": 7}]}, {"title": "Focus Management Tests", "file": "accessibility-audit.spec.ts", "line": 150, "column": 8, "specs": [{"title": "should have proper focus indicators on all interactive elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 3, "status": "failed", "duration": 19167, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(9)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByR<PERSON>('button').nth(9)\u001b[22m\n\u001b[2m    3 × locator resolved to <a id=\"c1\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m      - unexpected value \"inactive\"\u001b[22m\n\u001b[2m    10 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(9)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getBy<PERSON><PERSON>('button').nth(9)\u001b[22m\n\u001b[2m    3 × locator resolved to <a id=\"c1\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m      - unexpected value \"inactive\"\u001b[22m\n\u001b[2m    10 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "snippet": "\u001b[0m \u001b[90m 161 |\u001b[39m         \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misVisible() \u001b[33m&&\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misEnabled()) {\n \u001b[90m 162 |\u001b[39m           \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mfocus()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 163 |\u001b[39m           \u001b[36mawait\u001b[39m expect(button)\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 164 |\u001b[39m           \n \u001b[90m 165 |\u001b[39m           \u001b[90m// Check for enhanced focus class\u001b[39m\n \u001b[90m 166 |\u001b[39m           \u001b[36mconst\u001b[39m hasEnhancedFocus \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mevaluate(el \u001b[33m=>\u001b[39m \u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(9)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(9)\u001b[22m\n\u001b[2m    3 × locator resolved to <a id=\"c1\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m      - unexpected value \"inactive\"\u001b[22m\n\u001b[2m    10 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n\n  161 |         if (await button.isVisible() && await button.isEnabled()) {\n  162 |           await button.focus();\n> 163 |           await expect(button).toBeFocused();\n      |                                ^\n  164 |           \n  165 |           // Check for enhanced focus class\n  166 |           const hasEnhancedFocus = await button.evaluate(el => \n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:04.638Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-bde5361b245d12a953e8", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 2, "status": "passed", "duration": 7924, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:04.650Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "133fb274c06e7193ffc5-3f3e330ed4e0f04e92df", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 13, "parallelIndex": 3, "status": "timedOut", "duration": 32486, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 18, "line": 152}, "message": "Error: page.goto: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:8080/dashboard\", waiting until \"load\"\u001b[22m\n\n\n  150 |   test.describe('Focus Management Tests', () => {\n  151 |     test('should have proper focus indicators on all interactive elements', async ({ page }) => {\n> 152 |       await page.goto('http://localhost:8080/dashboard');\n      |                  ^\n  153 |       await expect(page.getByRole('main')).toBeVisible();\n  154 |\n  155 |       // Test focus on buttons\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:152:18"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:04:35.593Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-682887ca6a0d078ca50b", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 14, "parallelIndex": 1, "status": "timedOut", "duration": 34208, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 18, "line": 210}, "message": "Error: page.goto: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:8080/dashboard\", waiting until \"load\"\u001b[22m\n\n\n  208 |\n  209 |     test('should maintain focus visibility in modal contexts', async ({ page }) => {\n> 210 |       await page.goto('http://localhost:8080/dashboard');\n      |                  ^\n  211 |       \n  212 |       // Click RLS badge to open modal\n  213 |       const rlsBadge = page.getByTestId('rls-policy-badge');\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:210:18"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:04:35.702Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-6163f18125713d498b6d", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 21, "parallelIndex": 2, "status": "timedOut", "duration": 31152, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "message": "Error: \u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(9)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(9)\u001b[22m\n\u001b[2m    11 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n\n  161 |         if (await button.isVisible() && await button.isEnabled()) {\n  162 |           await button.focus();\n> 163 |           await expect(button).toBeFocused();\n      |                                ^\n  164 |           \n  165 |           // Check for enhanced focus class\n  166 |           const hasEnhancedFocus = await button.evaluate(el => \n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:57.411Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\\error-context.md"}]}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-73244f85a61b8414e137", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 22, "parallelIndex": 0, "status": "passed", "duration": 9189, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:59.265Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "133fb274c06e7193ffc5-24e5e3416c1a03456aaa", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 29, "parallelIndex": 0, "status": "failed", "duration": 22798, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(5)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(5)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(5)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(5)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "snippet": "\u001b[0m \u001b[90m 161 |\u001b[39m         \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misVisible() \u001b[33m&&\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misEnabled()) {\n \u001b[90m 162 |\u001b[39m           \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mfocus()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 163 |\u001b[39m           \u001b[36mawait\u001b[39m expect(button)\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 164 |\u001b[39m           \n \u001b[90m 165 |\u001b[39m           \u001b[90m// Check for enhanced focus class\u001b[39m\n \u001b[90m 166 |\u001b[39m           \u001b[36mconst\u001b[39m hasEnhancedFocus \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mevaluate(el \u001b[33m=>\u001b[39m \u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(5)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(5)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n\n  161 |         if (await button.isVisible() && await button.isEnabled()) {\n  162 |           await button.focus();\n> 163 |           await expect(button).toBeFocused();\n      |                                ^\n  164 |           \n  165 |           // Check for enhanced focus class\n  166 |           const hasEnhancedFocus = await button.evaluate(el => \n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:44.343Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-ccbfb2803ddb36326d41", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 30, "parallelIndex": 1, "status": "passed", "duration": 4447, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:51.039Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "133fb274c06e7193ffc5-5475c5d247282550932e", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 36, "parallelIndex": 3, "status": "failed", "duration": 28513, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(5)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(5)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c6\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(5)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(5)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c6\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "snippet": "\u001b[0m \u001b[90m 161 |\u001b[39m         \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misVisible() \u001b[33m&&\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misEnabled()) {\n \u001b[90m 162 |\u001b[39m           \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mfocus()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 163 |\u001b[39m           \u001b[36mawait\u001b[39m expect(button)\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 164 |\u001b[39m           \n \u001b[90m 165 |\u001b[39m           \u001b[90m// Check for enhanced focus class\u001b[39m\n \u001b[90m 166 |\u001b[39m           \u001b[36mconst\u001b[39m hasEnhancedFocus \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mevaluate(el \u001b[33m=>\u001b[39m \u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(5)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(5)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c6\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n\n  161 |         if (await button.isVisible() && await button.isEnabled()) {\n  162 |           await button.focus();\n> 163 |           await expect(button).toBeFocused();\n      |                                ^\n  164 |           \n  165 |           // Check for enhanced focus class\n  166 |           const hasEnhancedFocus = await button.evaluate(el => \n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:30.259Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-0341b9e86f73c35782fa", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 37, "parallelIndex": 1, "status": "passed", "duration": 9328, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:31.113Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "133fb274c06e7193ffc5-f8bfa5c34a10eaaacbef", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}, {"title": "should have proper focus indicators on all interactive elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 43, "parallelIndex": 2, "status": "failed", "duration": 16547, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(9)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(9)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(9)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(9)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "snippet": "\u001b[0m \u001b[90m 161 |\u001b[39m         \u001b[36mif\u001b[39m (\u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misVisible() \u001b[33m&&\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39misEnabled()) {\n \u001b[90m 162 |\u001b[39m           \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mfocus()\u001b[33m;\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 163 |\u001b[39m           \u001b[36mawait\u001b[39m expect(button)\u001b[33m.\u001b[39mtoBeFocused()\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 164 |\u001b[39m           \n \u001b[90m 165 |\u001b[39m           \u001b[90m// Check for enhanced focus class\u001b[39m\n \u001b[90m 166 |\u001b[39m           \u001b[36mconst\u001b[39m hasEnhancedFocus \u001b[33m=\u001b[39m \u001b[36mawait\u001b[39m button\u001b[33m.\u001b[39mevaluate(el \u001b[33m=>\u001b[39m \u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoBeFocused\u001b[2m()\u001b[22m\n\nLocator: getByRole('button').nth(9)\nExpected: focused\nReceived: inactive\nCall log:\n\u001b[2m  - Expect \"toBeFocused\" with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for getByRole('button').nth(9)\u001b[22m\n\u001b[2m    13 × locator resolved to <a id=\"c4\" role=\"button\">Find References</a>\u001b[22m\n\u001b[2m       - unexpected value \"inactive\"\u001b[22m\n\n\n  161 |         if (await button.isVisible() && await button.isEnabled()) {\n  162 |           await button.focus();\n> 163 |           await expect(button).toBeFocused();\n      |                                ^\n  164 |           \n  165 |           // Check for enhanced focus class\n  166 |           const hasEnhancedFocus = await button.evaluate(el => \n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:163:32"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:08:26.587Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 32, "line": 163}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-b11a441b9c9fe15a4a78", "file": "accessibility-audit.spec.ts", "line": 151, "column": 5}, {"title": "should maintain focus visibility in modal contexts", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 44, "parallelIndex": 0, "status": "passed", "duration": 5727, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:08:28.360Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "133fb274c06e7193ffc5-1c358357a22137a878ea", "file": "accessibility-audit.spec.ts", "line": 209, "column": 5}]}, {"title": "Color Contrast Tests", "file": "accessibility-audit.spec.ts", "line": 245, "column": 8, "specs": [{"title": "should have sufficient color contrast for all text elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 1, "status": "failed", "duration": 15164, "error": {"message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "snippet": "\u001b[0m \u001b[90m 267 |\u001b[39m       }\n \u001b[90m 268 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 269 |\u001b[39m       expect(contrastViolations\u001b[33m.\u001b[39mlength\u001b[33m,\u001b[39m \u001b[32m'Should have no color contrast violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 270 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 271 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 272 |\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  267 |       }\n  268 |\n> 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);\n      |                                                                                     ^\n  270 |     });\n  271 |   });\n  272 | });\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85"}], "stdout": [{"text": "Color contrast violations:\n"}, {"text": "- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "  Element: <div data-lov-id=\"src\\pages\\Dashboard.tsx:420:18\" data-lov-name=\"Badge\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"420\" data-component-file=\"Dashboard.tsx\" data-component-name=\"Badge\" data-component-content=\"%7B%22text%22%3A%22Dashboard%22%2C%22className%22%3A%22bg-primary%2F20%20text-primary%20border-primary%2F30%22%7D\" class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-secondary/80 bg-primary/20 text-primary border-primary/30\">\n"}, {"text": "  Target: .hover\\:bg-secondary\\/80\n"}, {"text": "  Element: <button data-lov-id=\"src\\components\\ConfigurationStatus.tsx:98:12\" data-lov-name=\"Button\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"98\" data-component-file=\"ConfigurationStatus.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Setup%20Wizard%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90\">\n"}, {"text": "  Target: .text-primary-foreground.bg-primary[data-component-line=\"98\"]\n"}, {"text": "  Element: <div class=\"line-numbers\" style=\"left:19px;width:23px;\">10</div>\n"}, {"text": "  Target: div:nth-child(12) > .line-numbers\n"}, {"text": "  Element: <div class=\"line-numbers\" style=\"left:19px;width:23px;\">11</div>\n"}, {"text": "  Target: div:nth-child(13) > .line-numbers\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\DiffViewer.tsx:108:10\" data-lov-name=\"p\" data-component-path=\"src\\components\\DiffViewer.tsx\" data-component-line=\"108\" data-component-file=\"DiffViewer.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22No%20changes%20available%22%2C%22className%22%3A%22text-lg%20font-medium%22%7D\" class=\"text-lg font-medium\">\n"}, {"text": "  Target: p[data-component-line=\"108\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\DiffViewer.tsx:109:10\" data-lov-name=\"p\" data-component-path=\"src\\components\\DiffViewer.tsx\" data-component-line=\"109\" data-component-file=\"DiffViewer.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22Run%20the%20reactor%20loop%20to%20see%20code%20transformations%22%2C%22className%22%3A%22text-sm%22%7D\" class=\"text-sm\">\n"}, {"text": "  Target: p[data-component-line=\"109\"]\n"}, {"text": "  Element: <button data-lov-id=\"src\\pages\\Dashboard.tsx:711:22\" data-lov-name=\"Button\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"711\" data-component-file=\"Dashboard.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Stream%22%2C%22className%22%3A%22text-xs%20h-6%20px-2%20focus-ring%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs h-6 px-2 focus-ring\" role=\"tab\" aria-selected=\"true\">\n"}, {"text": "  Target: button[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:711:22\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\StreamPanel.tsx:173:16\" data-lov-name=\"p\" data-component-path=\"src\\components\\StreamPanel.tsx\" data-component-line=\"173\" data-component-file=\"StreamPanel.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22No%20stream%20events%20yet%22%2C%22className%22%3A%22text-sm%22%7D\" class=\"text-sm\">\n"}, {"text": "  Target: p[data-component-line=\"173\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\StreamPanel.tsx:174:16\" data-lov-name=\"p\" data-component-path=\"src\\components\\StreamPanel.tsx\" data-component-line=\"174\" data-component-file=\"StreamPanel.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22Start%20the%20reactor%20loop%20to%20see%20real-time%20updates%22%2C%22className%22%3A%22text-xs%20text-slate-600%22%7D\" class=\"text-xs text-slate-600\">\n"}, {"text": "  Target: p[data-component-line=\"174\"]\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:03:05.498Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-bb4b4134f9d5daeba0db", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "firefox", "projectName": "firefox", "results": [{"workerIndex": 15, "parallelIndex": 2, "status": "timedOut", "duration": 32497, "error": {"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m", "stack": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, "errors": [{"message": "\u001b[31mTest timeout of 30000ms exceeded.\u001b[39m"}, {"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 18, "line": 247}, "message": "Error: page.goto: Test timeout of 30000ms exceeded.\nCall log:\n\u001b[2m  - navigating to \"http://localhost:8080/dashboard\", waiting until \"load\"\u001b[22m\n\n\n  245 |   test.describe('Color Contrast Tests', () => {\n  246 |     test('should have sufficient color contrast for all text elements', async ({ page }) => {\n> 247 |       await page.goto('http://localhost:8080/dashboard');\n      |                  ^\n  248 |       await expect(page.getByRole('main')).toBeVisible();\n  249 |\n  250 |       // Run axe audit specifically for color contrast\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:247:18"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:04:36.261Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\\error-context.md"}]}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-16aea9165625ad26a840", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "webkit", "projectName": "webkit", "results": [{"workerIndex": 23, "parallelIndex": 1, "status": "failed", "duration": 23440, "error": {"message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "snippet": "\u001b[0m \u001b[90m 267 |\u001b[39m       }\n \u001b[90m 268 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 269 |\u001b[39m       expect(contrastViolations\u001b[33m.\u001b[39mlength\u001b[33m,\u001b[39m \u001b[32m'Should have no color contrast violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 270 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 271 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 272 |\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  267 |       }\n  268 |\n> 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);\n      |                                                                                     ^\n  270 |     });\n  271 |   });\n  272 | });\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85"}], "stdout": [{"text": "Color contrast violations:\n"}, {"text": "- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "  Element: <div data-lov-id=\"src\\pages\\Dashboard.tsx:420:18\" data-lov-name=\"Badge\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"420\" data-component-file=\"Dashboard.tsx\" data-component-name=\"Badge\" data-component-content=\"%7B%22text%22%3A%22Dashboard%22%2C%22className%22%3A%22bg-primary%2F20%20text-primary%20border-primary%2F30%22%7D\" class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-secondary/80 bg-primary/20 text-primary border-primary/30\">\n"}, {"text": "  Target: .hover\\:bg-secondary\\/80\n"}, {"text": "  Element: <button data-lov-id=\"src\\components\\ConfigurationStatus.tsx:98:12\" data-lov-name=\"Button\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"98\" data-component-file=\"ConfigurationStatus.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Setup%20Wizard%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90\">\n"}, {"text": "  Target: .text-primary-foreground.bg-primary[data-component-line=\"98\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\DiffViewer.tsx:108:10\" data-lov-name=\"p\" data-component-path=\"src\\components\\DiffViewer.tsx\" data-component-line=\"108\" data-component-file=\"DiffViewer.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22No%20changes%20available%22%2C%22className%22%3A%22text-lg%20font-medium%22%7D\" class=\"text-lg font-medium\">\n"}, {"text": "  Target: p[data-component-line=\"108\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\DiffViewer.tsx:109:10\" data-lov-name=\"p\" data-component-path=\"src\\components\\DiffViewer.tsx\" data-component-line=\"109\" data-component-file=\"DiffViewer.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22Run%20the%20reactor%20loop%20to%20see%20code%20transformations%22%2C%22className%22%3A%22text-sm%22%7D\" class=\"text-sm\">\n"}, {"text": "  Target: p[data-component-line=\"109\"]\n"}, {"text": "  Element: <button data-lov-id=\"src\\pages\\Dashboard.tsx:711:22\" data-lov-name=\"Button\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"711\" data-component-file=\"Dashboard.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Stream%22%2C%22className%22%3A%22text-xs%20h-6%20px-2%20focus-ring%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs h-6 px-2 focus-ring\" role=\"tab\" aria-selected=\"true\">\n"}, {"text": "  Target: button[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:711:22\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\StreamPanel.tsx:173:16\" data-lov-name=\"p\" data-component-path=\"src\\components\\StreamPanel.tsx\" data-component-line=\"173\" data-component-file=\"StreamPanel.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22No%20stream%20events%20yet%22%2C%22className%22%3A%22text-sm%22%7D\" class=\"text-sm\">\n"}, {"text": "  Target: p[data-component-line=\"173\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\StreamPanel.tsx:174:16\" data-lov-name=\"p\" data-component-path=\"src\\components\\StreamPanel.tsx\" data-component-line=\"174\" data-component-file=\"StreamPanel.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22Start%20the%20reactor%20loop%20to%20see%20real-time%20updates%22%2C%22className%22%3A%22text-xs%20text-slate-600%22%7D\" class=\"text-xs text-slate-600\">\n"}, {"text": "  Target: p[data-component-line=\"174\"]\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:05:59.929Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-f999fe7c9dbff59febe0", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Chrome", "projectName": "Mobile Chrome", "results": [{"workerIndex": 30, "parallelIndex": 1, "status": "failed", "duration": 5578, "error": {"message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "snippet": "\u001b[0m \u001b[90m 267 |\u001b[39m       }\n \u001b[90m 268 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 269 |\u001b[39m       expect(contrastViolations\u001b[33m.\u001b[39mlength\u001b[33m,\u001b[39m \u001b[32m'Should have no color contrast violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 270 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 271 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 272 |\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  267 |       }\n  268 |\n> 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);\n      |                                                                                     ^\n  270 |     });\n  271 |   });\n  272 | });\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85"}], "stdout": [{"text": "Color contrast violations:\n"}, {"text": "- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "  Element: <button data-lov-id=\"src\\components\\ConfigurationStatus.tsx:98:12\" data-lov-name=\"Button\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"98\" data-component-file=\"ConfigurationStatus.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Setup%20Wizard%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90 w-full\">\n"}, {"text": "  Target: .text-primary-foreground\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:06:56.393Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\\video.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\\video-1.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-3c456bad02c863a75619", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Mobile Safari", "projectName": "Mobile Safari", "results": [{"workerIndex": 37, "parallelIndex": 1, "status": "failed", "duration": 20783, "error": {"message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "snippet": "\u001b[0m \u001b[90m 267 |\u001b[39m       }\n \u001b[90m 268 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 269 |\u001b[39m       expect(contrastViolations\u001b[33m.\u001b[39mlength\u001b[33m,\u001b[39m \u001b[32m'Should have no color contrast violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 270 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 271 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 272 |\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  267 |       }\n  268 |\n> 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);\n      |                                                                                     ^\n  270 |     });\n  271 |   });\n  272 | });\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85"}], "stdout": [{"text": "Color contrast violations:\n"}, {"text": "- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "  Element: <button data-lov-id=\"src\\components\\ConfigurationStatus.tsx:98:12\" data-lov-name=\"Button\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"98\" data-component-file=\"ConfigurationStatus.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Setup%20Wizard%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90 w-full\">\n"}, {"text": "  Target: .text-primary-foreground\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:07:40.812Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-e81fae6c3ec76829bff6", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}, {"title": "should have sufficient color contrast for all text elements", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "Tablet", "projectName": "Tablet", "results": [{"workerIndex": 45, "parallelIndex": 3, "status": "failed", "duration": 7476, "error": {"message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m", "stack": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85", "location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "snippet": "\u001b[0m \u001b[90m 267 |\u001b[39m       }\n \u001b[90m 268 |\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 269 |\u001b[39m       expect(contrastViolations\u001b[33m.\u001b[39mlength\u001b[33m,\u001b[39m \u001b[32m'Should have no color contrast violations'\u001b[39m)\u001b[33m.\u001b[39mtoBe(\u001b[35m0\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m     |\u001b[39m                                                                                     \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 270 |\u001b[39m     })\u001b[33m;\u001b[39m\n \u001b[90m 271 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 272 |\u001b[39m })\u001b[33m;\u001b[39m\u001b[0m"}, "errors": [{"location": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}, "message": "Error: Should have no color contrast violations\n\n\u001b[2mexpect(\u001b[22m\u001b[31mreceived\u001b[39m\u001b[2m).\u001b[22mtoBe\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m) // Object.is equality\u001b[22m\n\nExpected: \u001b[32m0\u001b[39m\nReceived: \u001b[31m1\u001b[39m\n\n  267 |       }\n  268 |\n> 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);\n      |                                                                                     ^\n  270 |     });\n  271 |   });\n  272 | });\n    at C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts:269:85"}], "stdout": [{"text": "Color contrast violations:\n"}, {"text": "- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds\n"}, {"text": "  Element: <div data-lov-id=\"src\\pages\\Dashboard.tsx:420:18\" data-lov-name=\"Badge\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"420\" data-component-file=\"Dashboard.tsx\" data-component-name=\"Badge\" data-component-content=\"%7B%22text%22%3A%22Dashboard%22%2C%22className%22%3A%22bg-primary%2F20%20text-primary%20border-primary%2F30%22%7D\" class=\"inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-secondary/80 bg-primary/20 text-primary border-primary/30\">\n"}, {"text": "  Target: .hover\\:bg-secondary\\/80\n"}, {"text": "  Element: <button data-lov-id=\"src\\components\\ConfigurationStatus.tsx:98:12\" data-lov-name=\"Button\" data-component-path=\"src\\components\\ConfigurationStatus.tsx\" data-component-line=\"98\" data-component-file=\"ConfigurationStatus.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Setup%20Wizard%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90\">\n"}, {"text": "  Target: .text-primary-foreground.bg-primary[data-component-line=\"98\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\DiffViewer.tsx:108:10\" data-lov-name=\"p\" data-component-path=\"src\\components\\DiffViewer.tsx\" data-component-line=\"108\" data-component-file=\"DiffViewer.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22No%20changes%20available%22%2C%22className%22%3A%22text-lg%20font-medium%22%7D\" class=\"text-lg font-medium\">\n"}, {"text": "  Target: p[data-component-line=\"108\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\DiffViewer.tsx:109:10\" data-lov-name=\"p\" data-component-path=\"src\\components\\DiffViewer.tsx\" data-component-line=\"109\" data-component-file=\"DiffViewer.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22Run%20the%20reactor%20loop%20to%20see%20code%20transformations%22%2C%22className%22%3A%22text-sm%22%7D\" class=\"text-sm\">\n"}, {"text": "  Target: p[data-component-line=\"109\"]\n"}, {"text": "  Element: <button data-lov-id=\"src\\pages\\Dashboard.tsx:711:22\" data-lov-name=\"Button\" data-component-path=\"src\\pages\\Dashboard.tsx\" data-component-line=\"711\" data-component-file=\"Dashboard.tsx\" data-component-name=\"Button\" data-component-content=\"%7B%22text%22%3A%22Stream%22%2C%22className%22%3A%22text-xs%20h-6%20px-2%20focus-ring%22%7D\" class=\"inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs h-6 px-2 focus-ring\" role=\"tab\" aria-selected=\"true\">\n"}, {"text": "  Target: button[data-lov-id=\"src\\\\pages\\\\Dashboard.tsx:711:22\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\StreamPanel.tsx:173:16\" data-lov-name=\"p\" data-component-path=\"src\\components\\StreamPanel.tsx\" data-component-line=\"173\" data-component-file=\"StreamPanel.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22No%20stream%20events%20yet%22%2C%22className%22%3A%22text-sm%22%7D\" class=\"text-sm\">\n"}, {"text": "  Target: p[data-component-line=\"173\"]\n"}, {"text": "  Element: <p data-lov-id=\"src\\components\\StreamPanel.tsx:174:16\" data-lov-name=\"p\" data-component-path=\"src\\components\\StreamPanel.tsx\" data-component-line=\"174\" data-component-file=\"StreamPanel.tsx\" data-component-name=\"p\" data-component-content=\"%7B%22text%22%3A%22Start%20the%20reactor%20loop%20to%20see%20real-time%20updates%22%2C%22className%22%3A%22text-xs%20text-slate-600%22%7D\" class=\"text-xs text-slate-600\">\n"}, {"text": "  Target: p[data-component-line=\"174\"]\n"}], "stderr": [], "retry": 0, "startTime": "2025-06-25T15:08:29.789Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\\test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\\video-1.webm"}, {"name": "video", "contentType": "video/webm", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\\video.webm"}, {"name": "error-context", "contentType": "text/markdown", "path": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\artifacts\\test-results\\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\\error-context.md"}], "errorLocation": {"file": "C:\\Users\\<USER>\\Desktop\\metamorphic_reactor\\code-alchemy-reactor\\e2e\\accessibility-audit.spec.ts", "column": 85, "line": 269}}], "status": "unexpected"}], "id": "133fb274c06e7193ffc5-c64dca5632c22fdba5d2", "file": "accessibility-audit.spec.ts", "line": 246, "column": 5}]}]}]}], "errors": [], "stats": {"startTime": "2025-06-25T15:02:28.254Z", "duration": 375466.457, "expected": 5, "skipped": 0, "unexpected": 43, "flaky": 0}}