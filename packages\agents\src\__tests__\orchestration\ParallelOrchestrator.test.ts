import { jest } from '@jest/globals';
import { ParallelOrchestrator } from '../../orchestration/ParallelOrchestrator.js';
import { PlanAgent } from '../../planAgent.js';
import { CritiqueAgent } from '../../critiqueAgent.js';
import { CostGuard } from '../../utils/CostGuard.js';
import { TokenMonitor } from '../../utils/TokenMonitor.js';
import { ProviderFailover } from '../../utils/ProviderFailover.js';
import { createMockProviderConfig } from '../setup.js';
import { PlanRequest, CritiqueRequest, JSONPatch, CritiqueResult } from '../../types.js';

// Mock all dependencies
jest.mock('../../planAgent.js');
jest.mock('../../critiqueAgent.js');
jest.mock('../../utils/CostGuard.js');
jest.mock('../../utils/TokenMonitor.js');
jest.mock('../../utils/ProviderFailover.js');

describe('ParallelOrchestrator', () => {
  let orchestrator: ParallelOrchestrator;
  let mockPlanAgent: jest.Mocked<PlanAgent>;
  let mockCritiqueAgent: jest.Mocked<CritiqueAgent>;
  let mockCostGuard: jest.Mocked<CostGuard>;
  let mockTokenMonitor: jest.Mocked<TokenMonitor>;
  let mockFailover: jest.Mocked<ProviderFailover>;

  const mockPatch: JSONPatch = {
    operations: [{ op: 'add', path: '/test', value: 'test' }],
    description: 'Test patch',
    confidence: 0.9
  };

  const mockCritique: CritiqueResult = {
    score: 0.85,
    feedback: 'Good implementation',
    suggestions: ['Add tests'],
    isAcceptable: false
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Create mock instances
    mockPlanAgent = {
      generatePatch: jest.fn(),
      getProviderInfo: jest.fn(),
      getStats: jest.fn()
    } as any;

    mockCritiqueAgent = {
      scorePatch: jest.fn(),
      getProviderInfo: jest.fn(),
      getStats: jest.fn()
    } as any;

    mockCostGuard = {
      checkCostLimit: jest.fn(),
      recordCost: jest.fn(),
      getCurrentCost: jest.fn(),
      getRemainingBudget: jest.fn(),
      getStats: jest.fn()
    } as any;

    mockTokenMonitor = {
      recordUsage: jest.fn(),
      getStats: jest.fn(),
      isNearLimit: jest.fn()
    } as any;

    mockFailover = {
      executeWithFailover: jest.fn(),
      getStats: jest.fn()
    } as any;

    // Mock constructors
    (PlanAgent as jest.MockedClass<typeof PlanAgent>).mockImplementation(() => mockPlanAgent);
    (CritiqueAgent as jest.MockedClass<typeof CritiqueAgent>).mockImplementation(() => mockCritiqueAgent);
    (CostGuard as jest.MockedClass<typeof CostGuard>).mockImplementation(() => mockCostGuard);
    (TokenMonitor as jest.MockedClass<typeof TokenMonitor>).mockImplementation(() => mockTokenMonitor);
    (ProviderFailover as jest.MockedClass<typeof ProviderFailover>).mockImplementation(() => mockFailover);

    // Create orchestrator
    orchestrator = new ParallelOrchestrator({
      planConfig: createMockProviderConfig('openai'),
      critiqueConfig: createMockProviderConfig('vertex-ai'),
      costGuard: { maxCostPerLoop: 2.0, enabled: true },
      tokenMonitor: { enabled: true, maxEvents: 1000 },
      failover: { enabled: true, maxAttempts: 3 }
    });
  });

  describe('Basic Orchestration', () => {
    it('should execute plan-critique loop successfully', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication',
        context: { framework: 'React' }
      };

      // Mock successful responses
      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);
      mockCostGuard.getRemainingBudget.mockReturnValue(1.5);

      const result = await orchestrator.executeLoop(request);

      expect(result.finalPatch).toEqual(mockPatch);
      expect(result.finalScore).toBe(0.85);
      expect(result.iterations).toBe(1);
      expect(result.success).toBe(true);
      expect(mockPlanAgent.generatePatch).toHaveBeenCalledWith(request);
      expect(mockCritiqueAgent.scorePatch).toHaveBeenCalledWith({
        patch: mockPatch,
        originalPrompt: request.prompt,
        context: request.context
      });
    });

    it('should iterate until score threshold is met', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      // Mock multiple iterations
      const lowScoreCritique = { ...mockCritique, score: 0.7, isAcceptable: false };
      const highScoreCritique = { ...mockCritique, score: 0.95, isAcceptable: true };

      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch
        .mockResolvedValueOnce(lowScoreCritique)
        .mockResolvedValueOnce(highScoreCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);

      const result = await orchestrator.executeLoop(request, { scoreThreshold: 0.95 });

      expect(result.iterations).toBe(2);
      expect(result.finalScore).toBe(0.95);
      expect(result.success).toBe(true);
      expect(mockPlanAgent.generatePatch).toHaveBeenCalledTimes(2);
      expect(mockCritiqueAgent.scorePatch).toHaveBeenCalledTimes(2);
    });

    it('should stop at max iterations', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      const lowScoreCritique = { ...mockCritique, score: 0.7, isAcceptable: false };
      
      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(lowScoreCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);

      const result = await orchestrator.executeLoop(request, { 
        maxIterations: 3,
        scoreThreshold: 0.95 
      });

      expect(result.iterations).toBe(3);
      expect(result.finalScore).toBe(0.7);
      expect(result.success).toBe(false);
      expect(result.reason).toBe('max_iterations');
    });
  });

  describe('Cost Management', () => {
    it('should enforce cost limits', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      mockCostGuard.checkCostLimit.mockImplementation(() => {
        throw new Error('Cost limit exceeded');
      });

      await expect(orchestrator.executeLoop(request)).rejects.toThrow('Cost limit exceeded');
      expect(mockCostGuard.checkCostLimit).toHaveBeenCalled();
    });

    it('should track costs throughout execution', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);

      await orchestrator.executeLoop(request);

      expect(mockCostGuard.checkCostLimit).toHaveBeenCalled();
      expect(mockCostGuard.getCurrentCost).toHaveBeenCalled();
    });

    it('should stop when approaching cost limit', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      const lowScoreCritique = { ...mockCritique, score: 0.7, isAcceptable: false };
      
      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(lowScoreCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(1.9); // Near limit
      mockCostGuard.getRemainingBudget.mockReturnValue(0.1);

      const result = await orchestrator.executeLoop(request, { 
        costThreshold: 0.9 // 90% of budget
      });

      expect(result.success).toBe(false);
      expect(result.reason).toBe('cost_limit');
    });
  });

  describe('Error Handling', () => {
    it('should handle plan agent failures', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      mockPlanAgent.generatePatch.mockRejectedValue(new Error('Plan agent failed'));

      const result = await orchestrator.executeLoop(request);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Plan agent failed');
    });

    it('should handle critique agent failures', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockRejectedValue(new Error('Critique agent failed'));

      const result = await orchestrator.executeLoop(request);

      expect(result.success).toBe(false);
      expect(result.error).toBeDefined();
      expect(result.error?.message).toBe('Critique agent failed');
    });

    it('should retry on transient failures', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      mockPlanAgent.generatePatch
        .mockRejectedValueOnce(new Error('Temporary failure'))
        .mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);

      const result = await orchestrator.executeLoop(request, { retryAttempts: 2 });

      expect(result.success).toBe(true);
      expect(mockPlanAgent.generatePatch).toHaveBeenCalledTimes(2);
    });
  });

  describe('Parallel Execution', () => {
    it('should execute multiple requests in parallel', async () => {
      const requests: PlanRequest[] = [
        { prompt: 'Add authentication' },
        { prompt: 'Add validation' },
        { prompt: 'Add error handling' }
      ];

      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);

      const results = await orchestrator.executeBatch(requests);

      expect(results).toHaveLength(3);
      expect(results.every(r => r.success)).toBe(true);
      expect(mockPlanAgent.generatePatch).toHaveBeenCalledTimes(3);
      expect(mockCritiqueAgent.scorePatch).toHaveBeenCalledTimes(3);
    });

    it('should handle partial failures in batch execution', async () => {
      const requests: PlanRequest[] = [
        { prompt: 'Add authentication' },
        { prompt: 'Add validation' }
      ];

      mockPlanAgent.generatePatch
        .mockResolvedValueOnce(mockPatch)
        .mockRejectedValueOnce(new Error('Second request failed'));
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);

      const results = await orchestrator.executeBatch(requests);

      expect(results).toHaveLength(2);
      expect(results[0].success).toBe(true);
      expect(results[1].success).toBe(false);
    });

    it('should respect concurrency limits', async () => {
      const requests: PlanRequest[] = Array(10).fill(null).map((_, i) => ({
        prompt: `Request ${i}`
      }));

      let concurrentCalls = 0;
      let maxConcurrentCalls = 0;

      mockPlanAgent.generatePatch.mockImplementation(async () => {
        concurrentCalls++;
        maxConcurrentCalls = Math.max(maxConcurrentCalls, concurrentCalls);
        
        await new Promise(resolve => setTimeout(resolve, 10));
        
        concurrentCalls--;
        return mockPatch;
      });
      
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);

      await orchestrator.executeBatch(requests, { concurrency: 3 });

      expect(maxConcurrentCalls).toBeLessThanOrEqual(3);
    });
  });

  describe('Monitoring and Stats', () => {
    it('should provide execution statistics', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockCostGuard.getCurrentCost.mockReturnValue(0.5);
      mockCostGuard.getStats.mockReturnValue({
        totalCost: 0.5,
        totalTokens: 1000,
        totalRequests: 2,
        remainingBudget: 1.5,
        isNearLimit: false,
        costByProvider: new Map()
      });

      const result = await orchestrator.executeLoop(request);

      expect(result.stats).toBeDefined();
      expect(result.stats.totalCost).toBe(0.5);
      expect(result.stats.iterations).toBe(1);
      expect(result.executionTime).toBeGreaterThan(0);
    });

    it('should track token usage', async () => {
      const request: PlanRequest = {
        prompt: 'Add user authentication'
      };

      mockPlanAgent.generatePatch.mockResolvedValue(mockPatch);
      mockCritiqueAgent.scorePatch.mockResolvedValue(mockCritique);
      mockTokenMonitor.getStats.mockReturnValue({
        totalTokens: 1500,
        requestCount: 2,
        averageCost: 0.25,
        totalCost: 0.5
      });

      const result = await orchestrator.executeLoop(request);

      expect(mockTokenMonitor.recordUsage).toHaveBeenCalled();
      expect(result.stats.totalTokens).toBe(1500);
    });
  });

  describe('Configuration', () => {
    it('should use custom configuration options', async () => {
      const customOrchestrator = new ParallelOrchestrator({
        planConfig: createMockProviderConfig('openai'),
        critiqueConfig: createMockProviderConfig('anthropic'),
        costGuard: { maxCostPerLoop: 5.0, enabled: true },
        tokenMonitor: { enabled: true, maxEvents: 2000 },
        failover: { enabled: false }
      });

      expect(customOrchestrator).toBeDefined();
    });

    it('should validate configuration on creation', () => {
      expect(() => {
        new ParallelOrchestrator({
          planConfig: createMockProviderConfig('openai'),
          critiqueConfig: createMockProviderConfig('vertex-ai'),
          costGuard: { maxCostPerLoop: -1, enabled: true }, // Invalid
          tokenMonitor: { enabled: true, maxEvents: 1000 },
          failover: { enabled: true, maxAttempts: 3 }
        });
      }).toThrow();
    });
  });
});
