import { test, expect } from '@playwright/test';
import AxeBuilder from '@axe-core/playwright';

test.describe('WCAG 2.2 Accessibility Tests', () => {
  test('Dashboard should meet WCAG 2.2 AA standards', async ({ page }) => {
    // Navigate to the dashboard
    await page.goto('http://localhost:8080/dashboard');
    
    // Wait for the page to load completely
    await page.waitForLoadState('networkidle');
    
    // Run axe accessibility scan
    const accessibilityScanResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      .analyze();
    
    // Log results for debugging
    console.log(`Accessibility violations found: ${accessibilityScanResults.violations.length}`);
    
    if (accessibilityScanResults.violations.length > 0) {
      console.log('Violations:', JSON.stringify(accessibilityScanResults.violations, null, 2));
    }
    
    // Calculate accessibility score (100 - violations as percentage)
    const totalChecks = accessibilityScanResults.passes.length + accessibilityScanResults.violations.length;
    const score = totalChecks > 0 ? ((accessibilityScanResults.passes.length / totalChecks) * 100) : 0;
    
    console.log(`Accessibility Score: ${score.toFixed(1)}%`);
    console.log(`Passes: ${accessibilityScanResults.passes.length}`);
    console.log(`Violations: ${accessibilityScanResults.violations.length}`);
    
    // Expect score to be >= 97% for WCAG 2.2 compliance
    expect(score).toBeGreaterThanOrEqual(97);
    
    // Expect no violations
    expect(accessibilityScanResults.violations).toEqual([]);
  });

  test('Mobile dashboard should meet WCAG 2.2 touch target requirements', async ({ page }) => {
    // Set mobile viewport
    await page.setViewportSize({ width: 320, height: 568 });
    
    // Navigate to the dashboard
    await page.goto('http://localhost:8080/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Check touch target sizes for all interactive elements
    const interactiveElements = await page.locator('button, a, input, [role="button"], [role="tab"]').all();
    
    for (const element of interactiveElements) {
      const boundingBox = await element.boundingBox();
      if (boundingBox) {
        // WCAG 2.2 requires minimum 44x44px touch targets
        expect(boundingBox.width).toBeGreaterThanOrEqual(44);
        expect(boundingBox.height).toBeGreaterThanOrEqual(44);
      }
    }
    
    // Run axe scan specifically for mobile
    const mobileAccessibilityResults = await new AxeBuilder({ page })
      .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      .analyze();
    
    const mobileScore = mobileAccessibilityResults.passes.length / 
      (mobileAccessibilityResults.passes.length + mobileAccessibilityResults.violations.length) * 100;
    
    console.log(`Mobile Accessibility Score: ${mobileScore.toFixed(1)}%`);
    
    expect(mobileScore).toBeGreaterThanOrEqual(97);
  });

  test('Focus management should comply with WCAG 2.2 Focus-Not-Obscured', async ({ page }) => {
    await page.goto('http://localhost:8080/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Get all focusable elements
    const focusableElements = await page.locator('button, a, input, select, textarea, [tabindex]:not([tabindex="-1"])').all();
    
    for (const element of focusableElements) {
      // Focus the element
      await element.focus();
      
      // Check if element is visible and not obscured
      const isVisible = await element.isVisible();
      expect(isVisible).toBe(true);
      
      // Check if element is in viewport
      const boundingBox = await element.boundingBox();
      if (boundingBox) {
        const viewport = page.viewportSize();
        if (viewport) {
          expect(boundingBox.x).toBeGreaterThanOrEqual(0);
          expect(boundingBox.y).toBeGreaterThanOrEqual(0);
          expect(boundingBox.x + boundingBox.width).toBeLessThanOrEqual(viewport.width);
          expect(boundingBox.y + boundingBox.height).toBeLessThanOrEqual(viewport.height);
        }
      }
    }
  });

  test('Focus appearance should meet WCAG 2.2 standards', async ({ page }) => {
    await page.goto('http://localhost:8080/dashboard');
    await page.waitForLoadState('networkidle');
    
    // Test focus indicators on buttons
    const buttons = await page.locator('button').all();
    
    for (const button of buttons.slice(0, 5)) { // Test first 5 buttons
      await button.focus();
      
      // Check for focus outline
      const focusOutline = await button.evaluate((el) => {
        const styles = window.getComputedStyle(el);
        return {
          outline: styles.outline,
          outlineWidth: styles.outlineWidth,
          outlineColor: styles.outlineColor,
          boxShadow: styles.boxShadow
        };
      });
      
      // Should have visible focus indicator (outline or box-shadow)
      const hasFocusIndicator = 
        focusOutline.outline !== 'none' || 
        focusOutline.boxShadow !== 'none' ||
        focusOutline.outlineWidth !== '0px';
      
      expect(hasFocusIndicator).toBe(true);
    }
  });
});
