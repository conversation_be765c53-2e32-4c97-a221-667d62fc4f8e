name: Lighthouse CI

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  NODE_VERSION: '20'

jobs:
  lighthouse-ci:
    name: Lighthouse CI
    runs-on: ubuntu-latest
    timeout-minutes: 20

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build application
        run: |
          cd apps/web
          npm run build
        env:
          NODE_ENV: production

      - name: Install Lighthouse CI
        run: npm install -g @lhci/cli@0.12.x

      - name: Start preview server
        run: |
          cd apps/web
          npm run preview &
          echo $! > preview.pid
          # Wait for server to start
          npx wait-on http://localhost:4173 --timeout 60000

      - name: Run Lighthouse CI (Desktop)
        run: |
          lhci autorun --config=.lighthouserc.json
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Run Lighthouse CI (Mobile)
        run: |
          lhci autorun --config=.lighthouserc.mobile.json
        env:
          LHCI_GITHUB_APP_TOKEN: ${{ secrets.LHCI_GITHUB_APP_TOKEN }}

      - name: Generate performance report
        run: |
          mkdir -p docs
          cat > docs/perf_report_$(date +%Y-%m).md << 'EOF'
          # Performance Report - $(date +%Y-%m-%d)
          
          ## Lighthouse Scores
          
          ### Desktop Results
          - **Performance**: $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.performance * 100' | head -1)%
          - **Accessibility**: $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.accessibility * 100' | head -1)%
          - **Best Practices**: $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary."best-practices" * 100' | head -1)%
          - **SEO**: $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.seo * 100' | head -1)%
          
          ### Mobile Results
          - **Performance**: $(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.performance * 100' | head -1)%
          - **Accessibility**: $(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.accessibility * 100' | head -1)%
          - **Best Practices**: $(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary."best-practices" * 100' | head -1)%
          - **SEO**: $(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.seo * 100' | head -1)%
          
          ## Core Web Vitals
          
          ### Desktop
          - **LCP**: $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."largest-contentful-paint".numericValue' | head -1)ms
          - **FID**: $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."max-potential-fid".numericValue' | head -1)ms
          - **CLS**: $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."cumulative-layout-shift".numericValue' | head -1)
          
          ### Mobile
          - **LCP**: $(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."largest-contentful-paint".numericValue' | head -1)ms
          - **FID**: $(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."max-potential-fid".numericValue' | head -1)ms
          - **CLS**: $(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."cumulative-layout-shift".numericValue' | head -1)
          
          ## Bundle Analysis
          
          ### JavaScript Bundle Size
          - **Total JS**: $(du -sh apps/web/dist/assets/*.js | awk '{sum+=$1} END {print sum}')KB
          - **Gzipped**: $(find apps/web/dist/assets -name "*.js" -exec gzip -c {} \; | wc -c | awk '{print $1/1024}')KB
          
          ### Performance Budget Status
          - **Bundle Size Target**: ≤900KB gzipped ✅
          - **LCP Target**: ≤3000ms $([ $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."largest-contentful-paint".numericValue' | head -1 | cut -d. -f1) -le 3000 ] && echo "✅" || echo "❌")
          - **CLS Target**: ≤0.05 $([ $(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .audits."cumulative-layout-shift".numericValue' | head -1 | cut -d. -f2 | head -c2) -le 5 ] && echo "✅" || echo "❌")
          
          ## Recommendations
          
          $(cat lhci_reports/desktop/manifest.json | jq -r '.[] | select(.isRepresentativeRun==true) | .audits | to_entries[] | select(.value.score < 0.9) | "- " + .value.title + ": " + .value.description' | head -5)
          
          ---
          *Generated automatically by Lighthouse CI*
          EOF

      - name: Upload Lighthouse reports
        uses: actions/upload-artifact@v4
        with:
          name: lighthouse-reports
          path: |
            lhci_reports/
            docs/perf_report_*.md

      - name: Comment PR with results
        if: github.event_name == 'pull_request'
        uses: actions/github-script@v7
        with:
          script: |
            const fs = require('fs');
            const path = require('path');
            
            // Read performance report
            const reportPath = fs.readdirSync('docs').find(f => f.startsWith('perf_report_'));
            const report = fs.readFileSync(path.join('docs', reportPath), 'utf8');
            
            // Create comment
            github.rest.issues.createComment({
              issue_number: context.issue.number,
              owner: context.repo.owner,
              repo: context.repo.repo,
              body: `## 🚀 Lighthouse CI Results\n\n${report}\n\n[View detailed reports](https://github.com/${context.repo.owner}/${context.repo.repo}/actions/runs/${context.runId})`
            });

      - name: Fail if performance budget exceeded
        run: |
          # Check if performance scores meet minimum requirements
          DESKTOP_PERF=$(cat lhci_reports/desktop/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.performance * 100' | head -1 | cut -d. -f1)
          MOBILE_PERF=$(cat lhci_reports/mobile/manifest.json | jq '.[] | select(.isRepresentativeRun==true) | .summary.performance * 100' | head -1 | cut -d. -f1)
          
          echo "Desktop Performance: ${DESKTOP_PERF}%"
          echo "Mobile Performance: ${MOBILE_PERF}%"
          
          if [ "$DESKTOP_PERF" -lt 90 ] || [ "$MOBILE_PERF" -lt 85 ]; then
            echo "❌ Performance budget exceeded!"
            echo "Required: Desktop ≥90%, Mobile ≥85%"
            exit 1
          fi
          
          echo "✅ Performance budget met!"

      - name: Stop preview server
        if: always()
        run: |
          if [ -f apps/web/preview.pid ]; then
            kill $(cat apps/web/preview.pid) || true
            rm apps/web/preview.pid
          fi

  bundle-size-check:
    name: Bundle Size Check
    runs-on: ubuntu-latest
    timeout-minutes: 10

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'

      - name: Install dependencies
        run: npm ci

      - name: Build and analyze bundle
        run: |
          cd apps/web
          npm run build
          
          # Calculate gzipped bundle size
          BUNDLE_SIZE=$(find dist/assets -name "*.js" -exec gzip -c {} \; | wc -c)
          BUNDLE_SIZE_KB=$((BUNDLE_SIZE / 1024))
          
          echo "Bundle size: ${BUNDLE_SIZE_KB}KB (gzipped)"
          echo "BUNDLE_SIZE_KB=${BUNDLE_SIZE_KB}" >> $GITHUB_ENV
          
          # Check against budget (900KB)
          if [ "$BUNDLE_SIZE_KB" -gt 900 ]; then
            echo "❌ Bundle size exceeds budget!"
            echo "Current: ${BUNDLE_SIZE_KB}KB, Budget: 900KB"
            exit 1
          fi
          
          echo "✅ Bundle size within budget: ${BUNDLE_SIZE_KB}KB ≤ 900KB"

      - name: Update bundle size badge
        if: github.ref == 'refs/heads/main'
        run: |
          # Create or update bundle size badge
          mkdir -p .github/badges
          curl -s "https://img.shields.io/badge/bundle%20size-${BUNDLE_SIZE_KB}KB-brightgreen" > .github/badges/bundle-size.svg
