import { test, expect } from '@playwright/test';

test.describe('Command Palette (⌘K/Ctrl+K)', () => {
  test.beforeEach(async ({ page }) => {
    await page.goto('http://localhost:8080/dashboard');
    await page.waitForLoadState('networkidle');
  });

  test('should open command palette with Cmd+K on Mac', async ({ page }) => {
    // Press Cmd+K (or Ctrl+K on Windows/Linux)
    await page.keyboard.press('Meta+k');
    
    // Check if command palette is visible
    const commandDialog = page.locator('[role="dialog"]').filter({ hasText: 'Type a command or search...' });
    await expect(commandDialog).toBeVisible();
    
    // Check if input is focused
    const commandInput = page.locator('input[placeholder="Type a command or search..."]');
    await expect(commandInput).toBeFocused();
  });

  test('should open command palette with Ctrl+K on Windows/Linux', async ({ page }) => {
    // Press Ctrl+K
    await page.keyboard.press('Control+k');
    
    // Check if command palette is visible
    const commandDialog = page.locator('[role="dialog"]').filter({ hasText: 'Type a command or search...' });
    await expect(commandDialog).toBeVisible();
  });

  test('should close command palette with Escape', async ({ page }) => {
    // Open command palette
    await page.keyboard.press('Meta+k');
    
    // Verify it's open
    const commandDialog = page.locator('[role="dialog"]').filter({ hasText: 'Type a command or search...' });
    await expect(commandDialog).toBeVisible();
    
    // Close with Escape
    await page.keyboard.press('Escape');
    
    // Verify it's closed
    await expect(commandDialog).not.toBeVisible();
  });

  test('should display navigation commands', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Check for navigation commands
    await expect(page.locator('text=Go to Dashboard')).toBeVisible();
    await expect(page.locator('text=Go to Monitoring')).toBeVisible();
    await expect(page.locator('text=Go to History')).toBeVisible();
    await expect(page.locator('text=Go to Settings')).toBeVisible();
  });

  test('should display reactor commands', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Check for reactor commands
    await expect(page.locator('text=Start Reactor')).toBeVisible();
    await expect(page.locator('text=Stop Reactor')).toBeVisible();
    await expect(page.locator('text=Reset Reactor')).toBeVisible();
  });

  test('should display session management commands', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Check for session commands
    await expect(page.locator('text=New Session')).toBeVisible();
    await expect(page.locator('text=Save Session')).toBeVisible();
    await expect(page.locator('text=Load Session')).toBeVisible();
  });

  test('should filter commands based on search input', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Type search query
    await page.fill('input[placeholder="Type a command or search..."]', 'reactor');
    
    // Should show reactor-related commands
    await expect(page.locator('text=Start Reactor')).toBeVisible();
    await expect(page.locator('text=Stop Reactor')).toBeVisible();
    await expect(page.locator('text=Reset Reactor')).toBeVisible();
    
    // Should not show unrelated commands
    await expect(page.locator('text=Go to Dashboard')).not.toBeVisible();
  });

  test('should execute navigation command', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Click on "Go to Monitoring" command
    await page.click('text=Go to Monitoring');
    
    // Should navigate to monitoring page
    await expect(page).toHaveURL(/.*\/monitoring/);
    
    // Command palette should be closed
    const commandDialog = page.locator('[role="dialog"]').filter({ hasText: 'Type a command or search...' });
    await expect(commandDialog).not.toBeVisible();
  });

  test('should execute theme toggle command', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Click on theme toggle command
    await page.click('text=Toggle Theme');
    
    // Command palette should be closed
    const commandDialog = page.locator('[role="dialog"]').filter({ hasText: 'Type a command or search...' });
    await expect(commandDialog).not.toBeVisible();
    
    // Theme should have changed (check for theme class on html element)
    const htmlElement = page.locator('html');
    const themeClass = await htmlElement.getAttribute('class');
    expect(themeClass).toContain('dark');
  });

  test('should show keyboard shortcuts for commands', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Check for keyboard shortcuts display
    await expect(page.locator('text=⌘ + R')).toBeVisible(); // Start Reactor shortcut
    await expect(page.locator('text=⌘ + N')).toBeVisible(); // New Session shortcut
    await expect(page.locator('text=⌘ + T')).toBeVisible(); // Toggle Theme shortcut
  });

  test('should group commands by category', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Check for group headings
    await expect(page.locator('text=Navigation')).toBeVisible();
    await expect(page.locator('text=Reactor')).toBeVisible();
    await expect(page.locator('text=Session')).toBeVisible();
    await expect(page.locator('text=Appearance')).toBeVisible();
    await expect(page.locator('text=Help')).toBeVisible();
  });

  test('should handle empty search results', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Type search query that won't match anything
    await page.fill('input[placeholder="Type a command or search..."]', 'nonexistentcommand');
    
    // Should show "No results found" message
    await expect(page.locator('text=No results found')).toBeVisible();
  });

  test('should be accessible with keyboard navigation', async ({ page }) => {
    await page.keyboard.press('Meta+k');
    
    // Input should be focused initially
    const commandInput = page.locator('input[placeholder="Type a command or search..."]');
    await expect(commandInput).toBeFocused();
    
    // Navigate down to first command
    await page.keyboard.press('ArrowDown');
    
    // First command should be highlighted/focused
    const firstCommand = page.locator('[cmdk-item]').first();
    await expect(firstCommand).toHaveAttribute('data-selected', 'true');
    
    // Press Enter to execute command
    await page.keyboard.press('Enter');
    
    // Command palette should close
    const commandDialog = page.locator('[role="dialog"]').filter({ hasText: 'Type a command or search...' });
    await expect(commandDialog).not.toBeVisible();
  });

  test('should maintain focus management for WCAG 2.2 compliance', async ({ page }) => {
    // Focus an element before opening command palette
    await page.focus('button:first-of-type');
    const initialFocusedElement = await page.evaluate(() => document.activeElement?.tagName);
    
    // Open command palette
    await page.keyboard.press('Meta+k');
    
    // Command input should be focused
    const commandInput = page.locator('input[placeholder="Type a command or search..."]');
    await expect(commandInput).toBeFocused();
    
    // Close command palette
    await page.keyboard.press('Escape');
    
    // Focus should return to previously focused element or body
    const finalFocusedElement = await page.evaluate(() => document.activeElement?.tagName);
    expect(['BUTTON', 'BODY']).toContain(finalFocusedElement);
  });
});
