<testsuites id="" name="" tests="48" failures="43" skipped="0" errors="0" time="375.466457">
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-25T15:02:28.925Z" hostname="chromium" tests="8" failures="7" skipped="0" time="154.318" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="23.54">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. aria-valid-attr-value: Ensure all ARIA attributes have valid values
   Impact: critical
   Help: ARIA attributes must conform to valid values
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 7
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="24.981">
<failure message="accessibility-audit.spec.ts:57:3 should pass axe audit on main dashboard" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:57:3 › Accessibility Audit › should pass axe audit on main dashboard 

    Error: Main dashboard should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      67 |     
    > 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);
         |                                                                                     ^
      69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);
      70 |   });
      71 |
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:68:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="24.676">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Mobile (320x568)" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568) 

    Error: Mobile viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="24.227">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Tablet (768x1024)" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024) 

    Error: Tablet viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="14.639">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Desktop (1280x720)" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720) 

    Error: Desktop viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="19.167">
<failure message="accessibility-audit.spec.ts:151:5 should have proper focus indicators on all interactive elements" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:151:5 › Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements 

    Error: Timed out 10000ms waiting for expect(locator).toBeFocused()

    Locator: getByRole('button').nth(9)
    Expected: focused
    Received: inactive
    Call log:
      - Expect "toBeFocused" with timeout 10000ms
      - waiting for getByRole('button').nth(9)
        3 × locator resolved to <a id="c1" role="button">Find References</a>
          - unexpected value "inactive"
        10 × locator resolved to <a id="c4" role="button">Find References</a>
           - unexpected value "inactive"


      161 |         if (await button.isVisible() && await button.isEnabled()) {
      162 |           await button.focus();
    > 163 |           await expect(button).toBeFocused();
          |                                ^
      164 |           
      165 |           // Check for enhanced focus class
      166 |           const hasEnhancedFocus = await button.evaluate(el => 
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:163:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-chromium\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="7.924">
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="15.164">
<failure message="accessibility-audit.spec.ts:246:5 should have sufficient color contrast for all text elements" type="FAILURE">
<![CDATA[  [chromium] › accessibility-audit.spec.ts:246:5 › Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements 

    Error: Should have no color contrast violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      267 |       }
      268 |
    > 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);
          |                                                                                     ^
      270 |     });
      271 |   });
      272 | });
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:269:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Color contrast violations:
- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
  Element: <div data-lov-id="src\pages\Dashboard.tsx:420:18" data-lov-name="Badge" data-component-path="src\pages\Dashboard.tsx" data-component-line="420" data-component-file="Dashboard.tsx" data-component-name="Badge" data-component-content="%7B%22text%22%3A%22Dashboard%22%2C%22className%22%3A%22bg-primary%2F20%20text-primary%20border-primary%2F30%22%7D" class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-secondary/80 bg-primary/20 text-primary border-primary/30">
  Target: .hover\:bg-secondary\/80
  Element: <button data-lov-id="src\components\ConfigurationStatus.tsx:98:12" data-lov-name="Button" data-component-path="src\components\ConfigurationStatus.tsx" data-component-line="98" data-component-file="ConfigurationStatus.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Setup%20Wizard%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90">
  Target: .text-primary-foreground.bg-primary[data-component-line="98"]
  Element: <div class="line-numbers" style="left:19px;width:23px;">10</div>
  Target: div:nth-child(12) > .line-numbers
  Element: <div class="line-numbers" style="left:19px;width:23px;">11</div>
  Target: div:nth-child(13) > .line-numbers
  Element: <p data-lov-id="src\components\DiffViewer.tsx:108:10" data-lov-name="p" data-component-path="src\components\DiffViewer.tsx" data-component-line="108" data-component-file="DiffViewer.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22No%20changes%20available%22%2C%22className%22%3A%22text-lg%20font-medium%22%7D" class="text-lg font-medium">
  Target: p[data-component-line="108"]
  Element: <p data-lov-id="src\components\DiffViewer.tsx:109:10" data-lov-name="p" data-component-path="src\components\DiffViewer.tsx" data-component-line="109" data-component-file="DiffViewer.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22Run%20the%20reactor%20loop%20to%20see%20code%20transformations%22%2C%22className%22%3A%22text-sm%22%7D" class="text-sm">
  Target: p[data-component-line="109"]
  Element: <button data-lov-id="src\pages\Dashboard.tsx:711:22" data-lov-name="Button" data-component-path="src\pages\Dashboard.tsx" data-component-line="711" data-component-file="Dashboard.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Stream%22%2C%22className%22%3A%22text-xs%20h-6%20px-2%20focus-ring%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs h-6 px-2 focus-ring" role="tab" aria-selected="true">
  Target: button[data-lov-id="src\\pages\\Dashboard.tsx:711:22"]
  Element: <p data-lov-id="src\components\StreamPanel.tsx:173:16" data-lov-name="p" data-component-path="src\components\StreamPanel.tsx" data-component-line="173" data-component-file="StreamPanel.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22No%20stream%20events%20yet%22%2C%22className%22%3A%22text-sm%22%7D" class="text-sm">
  Target: p[data-component-line="173"]
  Element: <p data-lov-id="src\components\StreamPanel.tsx:174:16" data-lov-name="p" data-component-path="src\components\StreamPanel.tsx" data-component-line="174" data-component-file="StreamPanel.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22Start%20the%20reactor%20loop%20to%20see%20real-time%20updates%22%2C%22className%22%3A%22text-xs%20text-slate-600%22%7D" class="text-xs text-slate-600">
  Target: p[data-component-line="174"]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-chromium\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-25T15:02:28.925Z" hostname="firefox" tests="8" failures="8" skipped="0" time="297.108" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="37.952">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Test timeout of 30000ms exceeded.

    Error: page.evaluate: Test timeout of 30000ms exceeded.
     Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md

      18 |   test('should pass axe accessibility audit on main dashboard', async ({ page }) => {
      19 |     // Run axe accessibility scan
    > 20 |     const accessibilityScanResults = await new AxeBuilder({ page })
         |                                      ^
      21 |       .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      22 |       .analyze();
      23 |
        at AxeBuilder.analyze (C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\node_modules\.pnpm\@axe-core+playwright@4.10.2_playwright-core@1.53.1\node_modules\@axe-core\playwright\dist\index.mjs:218:13)
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:20:38

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="44.029">
<failure message="accessibility-audit.spec.ts:57:3 should pass axe audit on main dashboard" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:57:3 › Accessibility Audit › should pass axe audit on main dashboard 

    Test timeout of 30000ms exceeded.

    Error: browserContext._wrapApiCall: Protocol error (Browser.removeBrowserContext): this._windows[aWindow.__SSi] is undefined

    Error: browserContext.newPage: Test timeout of 30000ms exceeded.
     Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md

      59 |     await expect(page.getByRole('main')).toBeVisible();
      60 |
    > 61 |     const accessibilityScanResults = await new AxeBuilder({ page })
         |                                      ^
      62 |       .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      63 |       .analyze();
      64 |
        at AxeBuilder.analyze (C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\node_modules\.pnpm\@axe-core+playwright@4.10.2_playwright-core@1.53.1\node_modules\@axe-core\playwright\dist\index.mjs:218:13)
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:61:38
]]>
</failure>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="37.396">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Mobile (320x568)" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568) 

    Test timeout of 30000ms exceeded while running "beforeEach" hook.

      10 |
      11 | test.describe('Accessibility Audit', () => {
    > 12 |   test.beforeEach(async ({ page }) => {
         |        ^
      13 |     // Navigate to main dashboard
      14 |     await page.goto('http://localhost:8080/dashboard');
      15 |     await expect(page.getByRole('main')).toBeVisible();
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:12:8

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="39.837">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Tablet (768x1024)" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024) 

    Test timeout of 30000ms exceeded.

    Error: page.goto: Test timeout of 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:8080/dashboard", waiting until "load"


      83 |
      84 |         // Navigate to main dashboard
    > 85 |         await page.goto('http://localhost:8080/dashboard');
         |                    ^
      86 |         await expect(page.getByRole('main')).toBeVisible();
      87 |
      88 |         // Take screenshot for visual verification
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:85:20

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--firefox\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="38.703">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Desktop (1280x720)" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720) 

    Test timeout of 30000ms exceeded.

    Error: page.evaluate: Test timeout of 30000ms exceeded.
     Please check out https://github.com/dequelabs/axe-core-npm/blob/develop/packages/playwright/error-handling.md

      93 |
      94 |         // Run accessibility audit at this viewport
    > 95 |         const accessibilityScanResults = await new AxeBuilder({ page })
         |                                          ^
      96 |           .withTags(['wcag2a', 'wcag2aa', 'wcag21aa', 'wcag22aa'])
      97 |           .analyze();
      98 |
        at AxeBuilder.analyze (C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\node_modules\.pnpm\@axe-core+playwright@4.10.2_playwright-core@1.53.1\node_modules\@axe-core\playwright\dist\index.mjs:218:13)
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:95:42

    attachment #1: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="32.486">
<failure message="accessibility-audit.spec.ts:151:5 should have proper focus indicators on all interactive elements" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:151:5 › Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements 

    Test timeout of 30000ms exceeded.

    Error: page.goto: Test timeout of 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:8080/dashboard", waiting until "load"


      150 |   test.describe('Focus Management Tests', () => {
      151 |     test('should have proper focus indicators on all interactive elements', async ({ page }) => {
    > 152 |       await page.goto('http://localhost:8080/dashboard');
          |                  ^
      153 |       await expect(page.getByRole('main')).toBeVisible();
      154 |
      155 |       // Test focus on buttons
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:152:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="34.208">
<failure message="accessibility-audit.spec.ts:209:5 should maintain focus visibility in modal contexts" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:209:5 › Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts 

    Test timeout of 30000ms exceeded.

    Error: page.goto: Test timeout of 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:8080/dashboard", waiting until "load"


      208 |
      209 |     test('should maintain focus visibility in modal contexts', async ({ page }) => {
    > 210 |       await page.goto('http://localhost:8080/dashboard');
          |                  ^
      211 |       
      212 |       // Click RLS badge to open modal
      213 |       const rlsBadge = page.getByTestId('rls-policy-badge');
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:210:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-58c63-isibility-in-modal-contexts-firefox\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="32.497">
<failure message="accessibility-audit.spec.ts:246:5 should have sufficient color contrast for all text elements" type="FAILURE">
<![CDATA[  [firefox] › accessibility-audit.spec.ts:246:5 › Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements 

    Test timeout of 30000ms exceeded.

    Error: page.goto: Test timeout of 30000ms exceeded.
    Call log:
      - navigating to "http://localhost:8080/dashboard", waiting until "load"


      245 |   test.describe('Color Contrast Tests', () => {
      246 |     test('should have sufficient color contrast for all text elements', async ({ page }) => {
    > 247 |       await page.goto('http://localhost:8080/dashboard');
          |                  ^
      248 |       await expect(page.getByRole('main')).toBeVisible();
      249 |
      250 |       // Run axe audit specifically for color contrast
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:247:18

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-firefox\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-25T15:02:28.925Z" hostname="webkit" tests="8" failures="7" skipped="0" time="182.412" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="22.408">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. aria-valid-attr-value: Ensure all ARIA attributes have valid values
   Impact: critical
   Help: ARIA attributes must conform to valid values
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 7
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="26.034">
<failure message="accessibility-audit.spec.ts:57:3 should pass axe audit on main dashboard" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:57:3 › Accessibility Audit › should pass axe audit on main dashboard 

    Error: Main dashboard should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      67 |     
    > 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);
         |                                                                                     ^
      69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);
      70 |   });
      71 |
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:68:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="21.407">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Mobile (320x568)" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568) 

    Error: Mobile viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="26.5">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Tablet (768x1024)" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024) 

    Error: Tablet viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="22.282">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Desktop (1280x720)" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720) 

    Error: Desktop viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="31.152">
<failure message="accessibility-audit.spec.ts:151:5 should have proper focus indicators on all interactive elements" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:151:5 › Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements 

    Test timeout of 30000ms exceeded.

    Error: expect(locator).toBeFocused()

    Locator: getByRole('button').nth(9)
    Expected: focused
    Received: inactive
    Call log:
      - Expect "toBeFocused" with timeout 10000ms
      - waiting for getByRole('button').nth(9)
        11 × locator resolved to <a id="c4" role="button">Find References</a>
           - unexpected value "inactive"


      161 |         if (await button.isVisible() && await button.isEnabled()) {
      162 |           await button.focus();
    > 163 |           await expect(button).toBeFocused();
          |                                ^
      164 |           
      165 |           // Check for enhanced focus class
      166 |           const hasEnhancedFocus = await button.evaluate(el => 
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:163:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-webkit\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="9.189">
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="23.44">
<failure message="accessibility-audit.spec.ts:246:5 should have sufficient color contrast for all text elements" type="FAILURE">
<![CDATA[  [webkit] › accessibility-audit.spec.ts:246:5 › Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements 

    Error: Should have no color contrast violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      267 |       }
      268 |
    > 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);
          |                                                                                     ^
      270 |     });
      271 |   });
      272 | });
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:269:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Color contrast violations:
- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
  Element: <div data-lov-id="src\pages\Dashboard.tsx:420:18" data-lov-name="Badge" data-component-path="src\pages\Dashboard.tsx" data-component-line="420" data-component-file="Dashboard.tsx" data-component-name="Badge" data-component-content="%7B%22text%22%3A%22Dashboard%22%2C%22className%22%3A%22bg-primary%2F20%20text-primary%20border-primary%2F30%22%7D" class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-secondary/80 bg-primary/20 text-primary border-primary/30">
  Target: .hover\:bg-secondary\/80
  Element: <button data-lov-id="src\components\ConfigurationStatus.tsx:98:12" data-lov-name="Button" data-component-path="src\components\ConfigurationStatus.tsx" data-component-line="98" data-component-file="ConfigurationStatus.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Setup%20Wizard%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90">
  Target: .text-primary-foreground.bg-primary[data-component-line="98"]
  Element: <p data-lov-id="src\components\DiffViewer.tsx:108:10" data-lov-name="p" data-component-path="src\components\DiffViewer.tsx" data-component-line="108" data-component-file="DiffViewer.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22No%20changes%20available%22%2C%22className%22%3A%22text-lg%20font-medium%22%7D" class="text-lg font-medium">
  Target: p[data-component-line="108"]
  Element: <p data-lov-id="src\components\DiffViewer.tsx:109:10" data-lov-name="p" data-component-path="src\components\DiffViewer.tsx" data-component-line="109" data-component-file="DiffViewer.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22Run%20the%20reactor%20loop%20to%20see%20code%20transformations%22%2C%22className%22%3A%22text-sm%22%7D" class="text-sm">
  Target: p[data-component-line="109"]
  Element: <button data-lov-id="src\pages\Dashboard.tsx:711:22" data-lov-name="Button" data-component-path="src\pages\Dashboard.tsx" data-component-line="711" data-component-file="Dashboard.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Stream%22%2C%22className%22%3A%22text-xs%20h-6%20px-2%20focus-ring%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs h-6 px-2 focus-ring" role="tab" aria-selected="true">
  Target: button[data-lov-id="src\\pages\\Dashboard.tsx:711:22"]
  Element: <p data-lov-id="src\components\StreamPanel.tsx:173:16" data-lov-name="p" data-component-path="src\components\StreamPanel.tsx" data-component-line="173" data-component-file="StreamPanel.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22No%20stream%20events%20yet%22%2C%22className%22%3A%22text-sm%22%7D" class="text-sm">
  Target: p[data-component-line="173"]
  Element: <p data-lov-id="src\components\StreamPanel.tsx:174:16" data-lov-name="p" data-component-path="src\components\StreamPanel.tsx" data-component-line="174" data-component-file="StreamPanel.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22Start%20the%20reactor%20loop%20to%20see%20real-time%20updates%22%2C%22className%22%3A%22text-xs%20text-slate-600%22%7D" class="text-xs text-slate-600">
  Target: p[data-component-line="174"]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-webkit\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-25T15:02:28.925Z" hostname="Mobile Chrome" tests="8" failures="7" skipped="0" time="85.915" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="7.658">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. button-name: Ensure buttons have discernible text
   Impact: critical
   Help: Buttons must have discernible text
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 1
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="6.424">
<failure message="accessibility-audit.spec.ts:57:3 should pass axe audit on main dashboard" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:57:3 › Accessibility Audit › should pass axe audit on main dashboard 

    Error: Main dashboard should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      67 |     
    > 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);
         |                                                                                     ^
      69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);
      70 |   });
      71 |
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:68:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="9.968">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Mobile (320x568)" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568) 

    Error: Mobile viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="12.494">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Tablet (768x1024)" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024) 

    Error: Tablet viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="16.548">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Desktop (1280x720)" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720) 

    Error: Desktop viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="22.798">
<failure message="accessibility-audit.spec.ts:151:5 should have proper focus indicators on all interactive elements" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:151:5 › Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements 

    Error: Timed out 10000ms waiting for expect(locator).toBeFocused()

    Locator: getByRole('button').nth(5)
    Expected: focused
    Received: inactive
    Call log:
      - Expect "toBeFocused" with timeout 10000ms
      - waiting for getByRole('button').nth(5)
        13 × locator resolved to <a id="c4" role="button">Find References</a>
           - unexpected value "inactive"


      161 |         if (await button.isVisible() && await button.isEnabled()) {
      162 |           await button.focus();
    > 163 |           await expect(button).toBeFocused();
          |                                ^
      164 |           
      165 |           // Check for enhanced focus class
      166 |           const hasEnhancedFocus = await button.evaluate(el => 
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:163:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="4.447">
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="5.578">
<failure message="accessibility-audit.spec.ts:246:5 should have sufficient color contrast for all text elements" type="FAILURE">
<![CDATA[  [Mobile Chrome] › accessibility-audit.spec.ts:246:5 › Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements 

    Error: Should have no color contrast violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      267 |       }
      268 |
    > 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);
          |                                                                                     ^
      270 |     });
      271 |   });
      272 | });
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:269:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Color contrast violations:
- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
  Element: <button data-lov-id="src\components\ConfigurationStatus.tsx:98:12" data-lov-name="Button" data-component-path="src\components\ConfigurationStatus.tsx" data-component-line="98" data-component-file="ConfigurationStatus.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Setup%20Wizard%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90 w-full">
  Target: .text-primary-foreground

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Chrome\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-25T15:02:28.925Z" hostname="Mobile Safari" tests="8" failures="7" skipped="0" time="161.989" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="15.543">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. button-name: Ensure buttons have discernible text
   Impact: critical
   Help: Buttons must have discernible text
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 1
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="18.969">
<failure message="accessibility-audit.spec.ts:57:3 should pass axe audit on main dashboard" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:57:3 › Accessibility Audit › should pass axe audit on main dashboard 

    Error: Main dashboard should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      67 |     
    > 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);
         |                                                                                     ^
      69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);
      70 |   });
      71 |
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:68:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="17.07">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Mobile (320x568)" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568) 

    Error: Mobile viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="24.163">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Tablet (768x1024)" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024) 

    Error: Tablet viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="27.62">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Desktop (1280x720)" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720) 

    Error: Desktop viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="28.513">
<failure message="accessibility-audit.spec.ts:151:5 should have proper focus indicators on all interactive elements" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:151:5 › Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements 

    Error: Timed out 10000ms waiting for expect(locator).toBeFocused()

    Locator: getByRole('button').nth(5)
    Expected: focused
    Received: inactive
    Call log:
      - Expect "toBeFocused" with timeout 10000ms
      - waiting for getByRole('button').nth(5)
        13 × locator resolved to <a id="c6" role="button">Find References</a>
           - unexpected value "inactive"


      161 |         if (await button.isVisible() && await button.isEnabled()) {
      162 |           await button.focus();
    > 163 |           await expect(button).toBeFocused();
          |                                ^
      164 |           
      165 |           // Check for enhanced focus class
      166 |           const hasEnhancedFocus = await button.evaluate(el => 
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:163:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="9.328">
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="20.783">
<failure message="accessibility-audit.spec.ts:246:5 should have sufficient color contrast for all text elements" type="FAILURE">
<![CDATA[  [Mobile Safari] › accessibility-audit.spec.ts:246:5 › Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements 

    Error: Should have no color contrast violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      267 |       }
      268 |
    > 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);
          |                                                                                     ^
      270 |     });
      271 |   });
      272 | });
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:269:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Color contrast violations:
- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
  Element: <button data-lov-id="src\components\ConfigurationStatus.tsx:98:12" data-lov-name="Button" data-component-path="src\components\ConfigurationStatus.tsx" data-component-line="98" data-component-file="ConfigurationStatus.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Setup%20Wizard%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90 w-full">
  Target: .text-primary-foreground

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Mobile-Safari\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
<testsuite name="accessibility-audit.spec.ts" timestamp="2025-06-25T15:02:28.925Z" hostname="Tablet" tests="8" failures="7" skipped="0" time="84.882" errors="0">
<testcase name="Accessibility Audit › should pass axe accessibility audit on main dashboard" classname="accessibility-audit.spec.ts" time="8.787">
<failure message="accessibility-audit.spec.ts:18:3 should pass axe accessibility audit on main dashboard" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:18:3 › Accessibility Audit › should pass axe accessibility audit on main dashboard 

    Error: Should have no critical accessibility violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      48 |
      49 |     // Expect no critical or serious violations
    > 50 |     expect(criticalViolations, 'Should have no critical accessibility violations').toBe(0);
         |                                                                                    ^
      51 |     expect(seriousViolations, 'Should have no serious accessibility violations').toBe(0);
      52 |     
      53 |     // Target score ≥ 97
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:50:84

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Accessibility violations: [33m2[39m
Violations details:
1. aria-valid-attr-value: Ensure all ARIA attributes have valid values
   Impact: critical
   Help: ARIA attributes must conform to valid values
   Nodes: 2
2. color-contrast: Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
   Impact: serious
   Help: Elements must meet minimum color contrast ratio thresholds
   Nodes: 7
Accessibility Score: 85/100
Critical: 1, Serious: 1, Moderate: 0, Minor: 0

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-0c677-ity-audit-on-main-dashboard-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › should pass axe audit on main dashboard" classname="accessibility-audit.spec.ts" time="9.011">
<failure message="accessibility-audit.spec.ts:57:3 should pass axe audit on main dashboard" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:57:3 › Accessibility Audit › should pass axe audit on main dashboard 

    Error: Main dashboard should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      66 |     const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      67 |     
    > 68 |     expect(criticalViolations, 'Main dashboard should have no critical violations').toBe(0);
         |                                                                                     ^
      69 |     expect(seriousViolations, 'Main dashboard should have no serious violations').toBe(0);
      70 |   });
      71 |
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:68:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-7dc18-axe-audit-on-main-dashboard-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568)" classname="accessibility-audit.spec.ts" time="13.466">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Mobile (320x568)" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Mobile (320x568) 

    Error: Mobile viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-b07ff-nctional-at-Mobile-320x568--Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024)" classname="accessibility-audit.spec.ts" time="13.05">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Tablet (768x1024)" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Tablet (768x1024) 

    Error: Tablet viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-a0f8f-ctional-at-Tablet-768x1024--Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720)" classname="accessibility-audit.spec.ts" time="10.818">
<failure message="accessibility-audit.spec.ts:80:7 should be accessible and functional at Desktop (1280x720)" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:80:7 › Accessibility Audit › Responsive Design Tests › should be accessible and functional at Desktop (1280x720) 

    Error: Desktop viewport should have no critical violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      100 |         const seriousViolations = accessibilityScanResults.violations.filter(v => v.impact === 'serious').length;
      101 |
    > 102 |         expect(criticalViolations, `${name} viewport should have no critical violations`).toBe(0);
          |                                                                                           ^
      103 |         expect(seriousViolations, `${name} viewport should have no serious violations`).toBe(0);
      104 |
      105 |         // Test key interactive elements are accessible
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:102:91

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-82e28-tional-at-Desktop-1280x720--Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements" classname="accessibility-audit.spec.ts" time="16.547">
<failure message="accessibility-audit.spec.ts:151:5 should have proper focus indicators on all interactive elements" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:151:5 › Accessibility Audit › Focus Management Tests › should have proper focus indicators on all interactive elements 

    Error: Timed out 10000ms waiting for expect(locator).toBeFocused()

    Locator: getByRole('button').nth(9)
    Expected: focused
    Received: inactive
    Call log:
      - Expect "toBeFocused" with timeout 10000ms
      - waiting for getByRole('button').nth(9)
        13 × locator resolved to <a id="c4" role="button">Find References</a>
           - unexpected value "inactive"


      161 |         if (await button.isVisible() && await button.isEnabled()) {
      162 |           await button.focus();
    > 163 |           await expect(button).toBeFocused();
          |                                ^
      164 |           
      165 |           // Check for enhanced focus class
      166 |           const hasEnhancedFocus = await button.evaluate(el => 
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:163:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-811ad-on-all-interactive-elements-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Accessibility Audit › Focus Management Tests › should maintain focus visibility in modal contexts" classname="accessibility-audit.spec.ts" time="5.727">
</testcase>
<testcase name="Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements" classname="accessibility-audit.spec.ts" time="7.476">
<failure message="accessibility-audit.spec.ts:246:5 should have sufficient color contrast for all text elements" type="FAILURE">
<![CDATA[  [Tablet] › accessibility-audit.spec.ts:246:5 › Accessibility Audit › Color Contrast Tests › should have sufficient color contrast for all text elements 

    Error: Should have no color contrast violations

    expect(received).toBe(expected) // Object.is equality

    Expected: 0
    Received: 1

      267 |       }
      268 |
    > 269 |       expect(contrastViolations.length, 'Should have no color contrast violations').toBe(0);
          |                                                                                     ^
      270 |     });
      271 |   });
      272 | });
        at C:\Users\<USER>\Desktop\metamorphic_reactor\code-alchemy-reactor\e2e\accessibility-audit.spec.ts:269:85

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\video-1.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #3: video (video/webm) ──────────────────────────────────────────────────────────────
    artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ..\artifacts\test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\error-context.md
]]>
</failure>
<system-out>
<![CDATA[Color contrast violations:
- Ensure the contrast between foreground and background colors meets WCAG 2 AA minimum contrast ratio thresholds
  Element: <div data-lov-id="src\pages\Dashboard.tsx:420:18" data-lov-name="Badge" data-component-path="src\pages\Dashboard.tsx" data-component-line="420" data-component-file="Dashboard.tsx" data-component-name="Badge" data-component-content="%7B%22text%22%3A%22Dashboard%22%2C%22className%22%3A%22bg-primary%2F20%20text-primary%20border-primary%2F30%22%7D" class="inline-flex items-center rounded-full border px-2.5 py-0.5 text-xs font-semibold transition-colors focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 hover:bg-secondary/80 bg-primary/20 text-primary border-primary/30">
  Target: .hover\:bg-secondary\/80
  Element: <button data-lov-id="src\components\ConfigurationStatus.tsx:98:12" data-lov-name="Button" data-component-path="src\components\ConfigurationStatus.tsx" data-component-line="98" data-component-file="ConfigurationStatus.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Setup%20Wizard%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap text-sm font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 text-primary-foreground h-9 rounded-md px-3 bg-primary hover:bg-primary/90">
  Target: .text-primary-foreground.bg-primary[data-component-line="98"]
  Element: <p data-lov-id="src\components\DiffViewer.tsx:108:10" data-lov-name="p" data-component-path="src\components\DiffViewer.tsx" data-component-line="108" data-component-file="DiffViewer.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22No%20changes%20available%22%2C%22className%22%3A%22text-lg%20font-medium%22%7D" class="text-lg font-medium">
  Target: p[data-component-line="108"]
  Element: <p data-lov-id="src\components\DiffViewer.tsx:109:10" data-lov-name="p" data-component-path="src\components\DiffViewer.tsx" data-component-line="109" data-component-file="DiffViewer.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22Run%20the%20reactor%20loop%20to%20see%20code%20transformations%22%2C%22className%22%3A%22text-sm%22%7D" class="text-sm">
  Target: p[data-component-line="109"]
  Element: <button data-lov-id="src\pages\Dashboard.tsx:711:22" data-lov-name="Button" data-component-path="src\pages\Dashboard.tsx" data-component-line="711" data-component-file="Dashboard.tsx" data-component-name="Button" data-component-content="%7B%22text%22%3A%22Stream%22%2C%22className%22%3A%22text-xs%20h-6%20px-2%20focus-ring%22%7D" class="inline-flex items-center justify-center gap-2 whitespace-nowrap font-medium ring-offset-background transition-colors focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2 focus-visible:z-10 disabled:pointer-events-none disabled:opacity-50 [&amp;_svg]:pointer-events-none [&amp;_svg]:size-4 [&amp;_svg]:shrink-0 bg-primary text-primary-foreground hover:bg-primary/90 rounded-md text-xs h-6 px-2 focus-ring" role="tab" aria-selected="true">
  Target: button[data-lov-id="src\\pages\\Dashboard.tsx:711:22"]
  Element: <p data-lov-id="src\components\StreamPanel.tsx:173:16" data-lov-name="p" data-component-path="src\components\StreamPanel.tsx" data-component-line="173" data-component-file="StreamPanel.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22No%20stream%20events%20yet%22%2C%22className%22%3A%22text-sm%22%7D" class="text-sm">
  Target: p[data-component-line="173"]
  Element: <p data-lov-id="src\components\StreamPanel.tsx:174:16" data-lov-name="p" data-component-path="src\components\StreamPanel.tsx" data-component-line="174" data-component-file="StreamPanel.tsx" data-component-name="p" data-component-content="%7B%22text%22%3A%22Start%20the%20reactor%20loop%20to%20see%20real-time%20updates%22%2C%22className%22%3A%22text-xs%20text-slate-600%22%7D" class="text-xs text-slate-600">
  Target: p[data-component-line="174"]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\test-failed-1.png]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\video-1.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\video.webm]]

[[ATTACHMENT|test-results\accessibility-audit-Access-1b8d2-trast-for-all-text-elements-Tablet\error-context.md]]
]]>
</system-out>
</testcase>
</testsuite>
</testsuites>