import { VertexAI } from '@google-cloud/vertexai';
import { 
  JSONPatch, 
  PlanRequest, 
  CritiqueRequest, 
  CritiqueResult, 
  ProviderConfig, 
  ProviderResponse, 
  TokenUsage 
} from '../types.js';
import { AgentProvider, ProviderError, RateLimitError } from './AgentProvider.js';
import { registerProvider } from './ProviderFactory.js';

/**
 * Vertex AI provider implementation using the Agent Development Kit
 */
export class VertexAIProvider extends AgentProvider {
  private vertexAI: VertexAI;
  private readonly modelPricing: Record<string, { input: number; output: number }> = {
    'gemini-2.5-flash': { input: 0.000075, output: 0.0003 },
    'gemini-2.5-pro': { input: 0.00125, output: 0.005 },
    'gemini-2.0-flash': { input: 0.000075, output: 0.0003 },
    'gemini-1.5-ultra': { input: 0.00125, output: 0.005 },
    'gemini-1.5-pro': { input: 0.00125, output: 0.005 },
    'gemini-1.5-flash': { input: 0.000075, output: 0.0003 },
  };

  constructor(config: ProviderConfig) {
    super(config);
    
    if (!config.vertexAI?.projectId) {
      throw new ProviderError('Vertex AI project ID is required', 'vertex-ai', 'MISSING_PROJECT_ID');
    }

    if (!config.vertexAI?.location) {
      throw new ProviderError('Vertex AI location is required', 'vertex-ai', 'MISSING_LOCATION');
    }

    this.vertexAI = new VertexAI({
      project: config.vertexAI.projectId,
      location: config.vertexAI.location,
      googleAuthOptions: config.vertexAI.credentials ? {
        credentials: config.vertexAI.credentials
      } : undefined,
    });
  }

  async validateConfig(): Promise<boolean> {
    try {
      // Test the configuration by getting a model instance
      const model = this.vertexAI.getGenerativeModel({
        model: this.config.model,
      });
      
      // Make a simple test request
      await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: 'Hello' }] }],
      });
      
      return true;
    } catch (error) {
      console.error('Vertex AI config validation failed:', error);
      return false;
    }
  }

  supportsStreaming(): boolean {
    return true;
  }

  async generatePatch(request: PlanRequest): Promise<ProviderResponse<JSONPatch>> {
    const startTime = Date.now();
    
    try {
      const model = this.vertexAI.getGenerativeModel({
        model: this.config.model,
        systemInstruction: this.buildSystemPrompt('planner', this.config.systemPrompt),
        generationConfig: {
          temperature: this.config.temperature,
          maxOutputTokens: this.config.maxTokens,
          responseMimeType: 'application/json',
        },
        tools: this.config.tools || [],
      });

      const userPrompt = this.buildPlanningPrompt(request);
      
      const response = await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      });

      const usage = this.createTokenUsage(response.response.usageMetadata);
      this.updateMetrics(usage);

      const content = response.response.candidates?.[0]?.content?.parts?.[0]?.text;
      if (!content) {
        throw new ProviderError('No content in Vertex AI response', 'vertex-ai', 'EMPTY_RESPONSE');
      }

      let patch: JSONPatch;
      try {
        const parsed = JSON.parse(content);
        patch = this.validateAndFormatPatch(parsed);
      } catch (error) {
        throw new ProviderError(
          `Failed to parse Vertex AI response as JSON: ${error instanceof Error ? error.message : 'Unknown error'}`,
          'vertex-ai',
          'INVALID_JSON'
        );
      }

      return {
        data: patch,
        usage,
        requestId: response.response.candidates?.[0]?.index?.toString(),
        latency: Date.now() - startTime,
      };

    } catch (error) {
      if (this.isRateLimitError(error)) {
        throw new RateLimitError('vertex-ai');
      }
      
      throw new ProviderError(
        `Vertex AI API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'vertex-ai',
        'API_ERROR'
      );
    }
  }

  async scorePatch(request: CritiqueRequest): Promise<ProviderResponse<CritiqueResult>> {
    const startTime = Date.now();
    
    try {
      const model = this.vertexAI.getGenerativeModel({
        model: this.config.model,
        systemInstruction: this.buildSystemPrompt('critic', this.config.systemPrompt),
        generationConfig: {
          temperature: this.config.temperature,
          maxOutputTokens: this.config.maxTokens,
          responseMimeType: 'application/json',
        },
      });

      const userPrompt = this.buildCritiquePrompt(request);
      
      const response = await model.generateContent({
        contents: [{ role: 'user', parts: [{ text: userPrompt }] }],
      });

      const usage = this.createTokenUsage(response.response.usageMetadata);
      this.updateMetrics(usage);

      const content = response.response.candidates?.[0]?.content?.parts?.[0]?.text;
      if (!content) {
        throw new ProviderError('No content in Vertex AI response', 'vertex-ai', 'EMPTY_RESPONSE');
      }

      let result: CritiqueResult;
      try {
        const parsed = JSON.parse(content);
        result = this.validateAndFormatCritique(parsed);
      } catch (error) {
        throw new ProviderError(
          `Failed to parse Vertex AI critique response: ${error instanceof Error ? error.message : 'Unknown error'}`,
          'vertex-ai',
          'INVALID_JSON'
        );
      }

      return {
        data: result,
        usage,
        requestId: response.response.candidates?.[0]?.index?.toString(),
        latency: Date.now() - startTime,
      };

    } catch (error) {
      if (this.isRateLimitError(error)) {
        throw new RateLimitError('vertex-ai');
      }
      
      throw new ProviderError(
        `Vertex AI API error: ${error instanceof Error ? error.message : 'Unknown error'}`,
        'vertex-ai',
        'API_ERROR'
      );
    }
  }

  protected calculateCost(usage: { inputTokens: number; outputTokens: number }): number {
    const pricing = this.modelPricing[this.config.model];
    if (!pricing) {
      console.warn(`No pricing data for model ${this.config.model}, using default rates`);
      return (usage.inputTokens * 0.0001 + usage.outputTokens * 0.0003) / 1000;
    }
    
    return (usage.inputTokens * pricing.input + usage.outputTokens * pricing.output) / 1000;
  }

  private createTokenUsage(usageMetadata: any): TokenUsage {
    const inputTokens = usageMetadata?.promptTokenCount || 0;
    const outputTokens = usageMetadata?.candidatesTokenCount || 0;
    const totalTokens = usageMetadata?.totalTokenCount || inputTokens + outputTokens;
    
    return {
      inputTokens,
      outputTokens,
      totalTokens,
      cost: this.calculateCost({ inputTokens, outputTokens }),
      provider: 'vertex-ai',
      model: this.config.model,
      timestamp: new Date(),
    };
  }

  private buildPlanningPrompt(request: PlanRequest): string {
    let prompt = `Generate a JSON patch to implement the following request:\n\n`;
    prompt += `Request: ${request.prompt}\n\n`;
    
    if (request.context) {
      prompt += `Context: ${JSON.stringify(request.context, null, 2)}\n\n`;
    }
    
    if (request.previousAttempts && request.previousAttempts.length > 0) {
      prompt += `Previous attempts (learn from these):\n`;
      request.previousAttempts.forEach((attempt, index) => {
        prompt += `Attempt ${index + 1}: ${attempt.description}\n`;
      });
      prompt += '\n';
    }
    
    prompt += `Return a JSON object with this exact structure:
{
  "operations": [/* JSON Patch operations */],
  "description": "Detailed description of the changes",
  "confidence": 0.85
}`;
    
    return prompt;
  }

  private buildCritiquePrompt(request: CritiqueRequest): string {
    let prompt = `Evaluate the following JSON patch:\n\n`;
    prompt += `Original Request: ${request.originalPrompt}\n\n`;
    prompt += `Proposed Patch: ${JSON.stringify(request.patch, null, 2)}\n\n`;
    
    if (request.context) {
      prompt += `Context: ${JSON.stringify(request.context, null, 2)}\n\n`;
    }
    
    prompt += `Evaluate on: correctness, completeness, quality, safety, and best practices.\n\n`;
    prompt += `Return a JSON object with this exact structure:
{
  "score": 0.85,
  "feedback": "Detailed feedback about the patch",
  "suggestions": ["Specific suggestion 1", "Specific suggestion 2"],
  "isAcceptable": true
}`;
    
    return prompt;
  }

  private validateAndFormatPatch(parsed: any): JSONPatch {
    if (!parsed.operations || !Array.isArray(parsed.operations)) {
      throw new Error('Missing or invalid operations array');
    }
    
    if (!parsed.description || typeof parsed.description !== 'string') {
      throw new Error('Missing or invalid description');
    }
    
    if (typeof parsed.confidence !== 'number' || parsed.confidence < 0 || parsed.confidence > 1) {
      throw new Error('Missing or invalid confidence score');
    }
    
    return {
      operations: parsed.operations,
      description: parsed.description,
      confidence: parsed.confidence,
    };
  }

  private validateAndFormatCritique(parsed: any): CritiqueResult {
    if (typeof parsed.score !== 'number' || parsed.score < 0 || parsed.score > 1) {
      throw new Error('Missing or invalid score');
    }
    
    if (!parsed.feedback || typeof parsed.feedback !== 'string') {
      throw new Error('Missing or invalid feedback');
    }
    
    if (!Array.isArray(parsed.suggestions)) {
      throw new Error('Missing or invalid suggestions array');
    }
    
    return {
      score: parsed.score,
      feedback: parsed.feedback,
      suggestions: parsed.suggestions,
      isAcceptable: parsed.isAcceptable ?? parsed.score >= 0.95,
    };
  }

  private isRateLimitError(error: any): boolean {
    return error?.status === 429 || 
           error?.code === 'RATE_LIMIT_EXCEEDED' ||
           error?.message?.includes('rate limit') ||
           error?.message?.includes('quota exceeded');
  }
}

// Auto-register the provider
registerProvider('vertex-ai', VertexAIProvider);
