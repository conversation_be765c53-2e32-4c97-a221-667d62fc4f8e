import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { 
  ErrorBoundary, 
  PageErrorBoundary, 
  ComponentErrorBoundary, 
  WidgetErrorBoundary,
  withErrorBoundary 
} from '../errors/ErrorBoundary';

// Mock error handler
jest.mock('@/lib/errors/errorHandler', () => ({
  ErrorHandler: {
    createError: jest.fn(() => ({ id: 'test-error-id' }))
  },
  ErrorCategory: {
    SYSTEM: 'SYSTEM'
  },
  ErrorSeverity: {
    HIGH: 'HIGH'
  }
}));

// Component that throws an error
const ThrowError: React.FC<{ shouldThrow?: boolean; message?: string }> = ({ 
  shouldThrow = true, 
  message = 'Test error' 
}) => {
  if (shouldThrow) {
    throw new Error(message);
  }
  return <div>No error</div>;
};

// Component that works normally
const WorkingComponent: React.FC = () => <div>Working component</div>;

describe('ErrorBoundary', () => {
  // Suppress console.error for these tests
  const originalError = console.error;
  beforeAll(() => {
    console.error = jest.fn();
  });

  afterAll(() => {
    console.error = originalError;
  });

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('Basic Error Handling', () => {
    it('should render children when no error occurs', () => {
      render(
        <ErrorBoundary>
          <WorkingComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText('Working component')).toBeInTheDocument();
    });

    it('should catch and display error when child component throws', () => {
      render(
        <ErrorBoundary>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      expect(screen.getByText(/test error/i)).toBeInTheDocument();
    });

    it('should call onError callback when error occurs', () => {
      const onError = jest.fn();
      
      render(
        <ErrorBoundary onError={onError}>
          <ThrowError message="Custom error" />
        </ErrorBoundary>
      );

      expect(onError).toHaveBeenCalledWith(
        expect.objectContaining({ message: 'Custom error' }),
        expect.objectContaining({ componentStack: expect.any(String) })
      );
    });

    it('should render custom fallback when provided', () => {
      const CustomFallback = <div>Custom error fallback</div>;
      
      render(
        <ErrorBoundary fallback={CustomFallback}>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Custom error fallback')).toBeInTheDocument();
    });
  });

  describe('Error Recovery', () => {
    it('should allow retry functionality', async () => {
      let shouldThrow = true;
      const RetryableComponent = () => <ThrowError shouldThrow={shouldThrow} />;
      
      const { rerender } = render(
        <ErrorBoundary>
          <RetryableComponent />
        </ErrorBoundary>
      );

      // Error should be displayed
      expect(screen.getByText(/something went wrong/i)).toBeInTheDocument();
      
      // Click retry button
      const retryButton = screen.getByText(/try again/i);
      
      // Fix the component before retrying
      shouldThrow = false;
      fireEvent.click(retryButton);

      // Force re-render
      rerender(
        <ErrorBoundary>
          <RetryableComponent />
        </ErrorBoundary>
      );

      await waitFor(() => {
        expect(screen.getByText('No error')).toBeInTheDocument();
      });
    });

    it('should show error details when requested', () => {
      render(
        <ErrorBoundary showDetails={true}>
          <ThrowError />
        </ErrorBoundary>
      );

      const detailsButton = screen.getByText(/details/i);
      fireEvent.click(detailsButton);

      expect(screen.getByText(/error details/i)).toBeInTheDocument();
    });

    it('should copy error details to clipboard', async () => {
      const mockWriteText = jest.fn().mockResolvedValue(undefined);
      Object.assign(navigator, {
        clipboard: {
          writeText: mockWriteText,
        },
      });

      render(
        <ErrorBoundary showDetails={true}>
          <ThrowError />
        </ErrorBoundary>
      );

      const detailsButton = screen.getByText(/details/i);
      fireEvent.click(detailsButton);

      const copyButton = screen.getByText(/copy error/i);
      fireEvent.click(copyButton);

      await waitFor(() => {
        expect(mockWriteText).toHaveBeenCalledWith(expect.stringContaining('Test error'));
      });
    });
  });

  describe('Error Levels', () => {
    it('should render widget-level error UI', () => {
      render(
        <ErrorBoundary level="widget">
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Widget Error')).toBeInTheDocument();
      expect(screen.getByText(/component encountered an error/i)).toBeInTheDocument();
    });

    it('should render component-level error UI', () => {
      render(
        <ErrorBoundary level="component">
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });

    it('should render page-level error UI', () => {
      render(
        <ErrorBoundary level="page">
          <ThrowError />
        </ErrorBoundary>
      );

      expect(screen.getByText(/page error/i)).toBeInTheDocument();
      expect(screen.getByText(/reload page/i)).toBeInTheDocument();
    });
  });

  describe('Specialized Error Boundaries', () => {
    it('should render PageErrorBoundary with correct level', () => {
      render(
        <PageErrorBoundary>
          <ThrowError />
        </PageErrorBoundary>
      );

      expect(screen.getByText(/page error/i)).toBeInTheDocument();
    });

    it('should render ComponentErrorBoundary with correct level', () => {
      render(
        <ComponentErrorBoundary>
          <ThrowError />
        </ComponentErrorBoundary>
      );

      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });

    it('should render WidgetErrorBoundary with correct level', () => {
      render(
        <WidgetErrorBoundary>
          <ThrowError />
        </WidgetErrorBoundary>
      );

      expect(screen.getByText('Widget Error')).toBeInTheDocument();
    });
  });

  describe('Higher-Order Component', () => {
    it('should wrap component with error boundary', () => {
      const WrappedComponent = withErrorBoundary(ThrowError, { level: 'component' });
      
      render(<WrappedComponent />);

      expect(screen.getByText('Component Error')).toBeInTheDocument();
    });

    it('should preserve component display name', () => {
      const TestComponent = () => <div>Test</div>;
      TestComponent.displayName = 'TestComponent';
      
      const WrappedComponent = withErrorBoundary(TestComponent);
      
      expect(WrappedComponent.displayName).toBe('withErrorBoundary(TestComponent)');
    });

    it('should use component name if no display name', () => {
      function TestComponent() {
        return <div>Test</div>;
      }
      
      const WrappedComponent = withErrorBoundary(TestComponent);
      
      expect(WrappedComponent.displayName).toBe('withErrorBoundary(TestComponent)');
    });
  });

  describe('Error Reporting', () => {
    it('should generate unique error IDs', () => {
      const onError1 = jest.fn();
      const onError2 = jest.fn();
      
      render(
        <div>
          <ErrorBoundary onError={onError1}>
            <ThrowError message="Error 1" />
          </ErrorBoundary>
          <ErrorBoundary onError={onError2}>
            <ThrowError message="Error 2" />
          </ErrorBoundary>
        </div>
      );

      // Both errors should be caught but have different IDs
      expect(onError1).toHaveBeenCalled();
      expect(onError2).toHaveBeenCalled();
    });

    it('should include component stack in error info', () => {
      const onError = jest.fn();
      
      render(
        <ErrorBoundary onError={onError}>
          <ThrowError />
        </ErrorBoundary>
      );

      expect(onError).toHaveBeenCalledWith(
        expect.any(Error),
        expect.objectContaining({
          componentStack: expect.stringContaining('ThrowError')
        })
      );
    });
  });

  describe('Edge Cases', () => {
    it('should handle errors in error boundary itself gracefully', () => {
      const BuggyErrorBoundary = ({ children }: { children: React.ReactNode }) => {
        throw new Error('Error boundary is broken');
      };
      
      // This should not crash the test
      expect(() => {
        render(
          <BuggyErrorBoundary>
            <WorkingComponent />
          </BuggyErrorBoundary>
        );
      }).toThrow();
    });

    it('should handle null/undefined children', () => {
      render(
        <ErrorBoundary>
          {null}
          {undefined}
        </ErrorBoundary>
      );

      // Should not crash
      expect(document.body).toBeInTheDocument();
    });

    it('should handle async errors in useEffect', async () => {
      const AsyncErrorComponent = () => {
        React.useEffect(() => {
          // Async errors are not caught by error boundaries
          // This test documents the limitation
          setTimeout(() => {
            throw new Error('Async error');
          }, 0);
        }, []);
        
        return <div>Async component</div>;
      };

      // This should render normally since error boundaries don't catch async errors
      render(
        <ErrorBoundary>
          <AsyncErrorComponent />
        </ErrorBoundary>
      );

      expect(screen.getByText('Async component')).toBeInTheDocument();
    });
  });
});
