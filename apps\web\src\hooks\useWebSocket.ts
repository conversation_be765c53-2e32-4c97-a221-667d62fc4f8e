import { useState, useEffect, useRef, useCallback } from 'react';

export interface WebSocketMessage {
  type: string;
  data: any;
  timestamp: number;
}

export interface WebSocketStatus {
  isConnected: boolean;
  connectionStatus: 'connecting' | 'connected' | 'disconnected' | 'error';
  lastMessage: WebSocketMessage | null;
  error: string | null;
  reconnectAttempts: number;
}

interface UseWebSocketOptions {
  url?: string;
  protocols?: string | string[];
  reconnectInterval?: number;
  maxReconnectAttempts?: number;
  onMessage?: (message: WebSocketMessage) => void;
  onConnect?: () => void;
  onDisconnect?: () => void;
  onError?: (error: Event) => void;
}

export function useWebSocket(options: UseWebSocketOptions = {}) {
  const {
    url = 'ws://localhost:8080/ws',
    protocols,
    reconnectInterval = 3000,
    maxReconnectAttempts = 5,
    onMessage,
    onConnect,
    onDisconnect,
    onError
  } = options;

  const [status, setStatus] = useState<WebSocketStatus>({
    isConnected: false,
    connectionStatus: 'disconnected',
    lastMessage: null,
    error: null,
    reconnectAttempts: 0
  });

  const wsRef = useRef<WebSocket | null>(null);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const reconnectAttemptsRef = useRef(0);

  const connect = useCallback(() => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      return;
    }

    setStatus(prev => ({ ...prev, connectionStatus: 'connecting', error: null }));

    try {
      wsRef.current = new WebSocket(url, protocols);

      wsRef.current.onopen = () => {
        setStatus(prev => ({
          ...prev,
          isConnected: true,
          connectionStatus: 'connected',
          error: null
        }));
        reconnectAttemptsRef.current = 0;
        onConnect?.();
      };

      wsRef.current.onmessage = (event) => {
        try {
          const message: WebSocketMessage = {
            type: 'message',
            data: JSON.parse(event.data),
            timestamp: Date.now()
          };
          
          setStatus(prev => ({ ...prev, lastMessage: message }));
          onMessage?.(message);
        } catch (error) {
          console.warn('Failed to parse WebSocket message:', error);
          const message: WebSocketMessage = {
            type: 'raw',
            data: event.data,
            timestamp: Date.now()
          };
          setStatus(prev => ({ ...prev, lastMessage: message }));
          onMessage?.(message);
        }
      };

      wsRef.current.onclose = () => {
        setStatus(prev => ({
          ...prev,
          isConnected: false,
          connectionStatus: 'disconnected'
        }));
        onDisconnect?.();

        // Attempt reconnection if within limits
        if (reconnectAttemptsRef.current < maxReconnectAttempts) {
          reconnectAttemptsRef.current++;
          setStatus(prev => ({ 
            ...prev, 
            reconnectAttempts: reconnectAttemptsRef.current 
          }));
          
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, reconnectInterval);
        }
      };

      wsRef.current.onerror = (error) => {
        setStatus(prev => ({
          ...prev,
          connectionStatus: 'error',
          error: 'WebSocket connection error'
        }));
        onError?.(error);
      };

    } catch (error) {
      setStatus(prev => ({
        ...prev,
        connectionStatus: 'error',
        error: error instanceof Error ? error.message : 'Unknown error'
      }));
    }
  }, [url, protocols, reconnectInterval, maxReconnectAttempts, onMessage, onConnect, onDisconnect, onError]);

  const disconnect = useCallback(() => {
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = null;
    }

    if (wsRef.current) {
      wsRef.current.close();
      wsRef.current = null;
    }

    reconnectAttemptsRef.current = 0;
    setStatus(prev => ({
      ...prev,
      isConnected: false,
      connectionStatus: 'disconnected',
      reconnectAttempts: 0
    }));
  }, []);

  const sendMessage = useCallback((data: any) => {
    if (wsRef.current?.readyState === WebSocket.OPEN) {
      try {
        const message = typeof data === 'string' ? data : JSON.stringify(data);
        wsRef.current.send(message);
        return true;
      } catch (error) {
        console.error('Failed to send WebSocket message:', error);
        return false;
      }
    }
    return false;
  }, []);

  // Auto-connect on mount
  useEffect(() => {
    connect();
    return () => {
      disconnect();
    };
  }, [connect, disconnect]);

  return {
    ...status,
    connect,
    disconnect,
    sendMessage
  };
}

// Hook specifically for monitoring WebSocket status
export function useWebSocketStatus(url?: string) {
  const { isConnected, connectionStatus, lastMessage, error, reconnectAttempts } = useWebSocket({
    url,
    maxReconnectAttempts: 3,
    reconnectInterval: 2000
  });

  // Simulate streaming status for demo purposes
  const [isStreaming, setIsStreaming] = useState(false);
  const [streamingProgress, setStreamingProgress] = useState(0);

  useEffect(() => {
    if (lastMessage?.type === 'stream_start') {
      setIsStreaming(true);
      setStreamingProgress(0);
    } else if (lastMessage?.type === 'stream_progress') {
      setStreamingProgress(lastMessage.data.progress || 0);
    } else if (lastMessage?.type === 'stream_end') {
      setIsStreaming(false);
      setStreamingProgress(100);
    }
  }, [lastMessage]);

  // Auto-update streaming progress every 400ms when streaming
  useEffect(() => {
    if (!isStreaming) return;

    const interval = setInterval(() => {
      setStreamingProgress(prev => {
        const next = prev + Math.random() * 10;
        return next >= 100 ? 100 : next;
      });
    }, 400);

    return () => clearInterval(interval);
  }, [isStreaming]);

  return {
    isConnected,
    connectionStatus,
    lastMessage,
    error,
    reconnectAttempts,
    isStreaming,
    streamingProgress: Math.round(streamingProgress)
  };
}

// Hook for real-time diff updates
export function useRealtimeDiff(originalCode: string, onDiffUpdate?: (diff: any) => void) {
  const { lastMessage, sendMessage } = useWebSocket({
    onMessage: (message) => {
      if (message.type === 'diff_update' && onDiffUpdate) {
        onDiffUpdate(message.data);
      }
    }
  });

  const requestDiff = useCallback((modifiedCode: string) => {
    sendMessage({
      type: 'request_diff',
      data: {
        original: originalCode,
        modified: modifiedCode,
        timestamp: Date.now()
      }
    });
  }, [originalCode, sendMessage]);

  return {
    requestDiff,
    lastDiffUpdate: lastMessage?.type === 'diff_update' ? lastMessage.data : null
  };
}
