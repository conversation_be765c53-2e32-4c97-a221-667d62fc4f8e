# Production Environment Configuration Template
# Copy this file to .env.production and fill in the actual values
# DO NOT commit the actual .env.production file to version control

# ================================
# Domain and SSL Configuration
# ================================
DOMAIN=your-domain.com
ACME_EMAIL=<EMAIL>
CORS_ORIGIN=https://your-domain.com

# ================================
# Supabase Configuration
# ================================
SUPABASE_URL=https://your-project.supabase.co
SUPABASE_ANON_KEY=your-anon-key
SUPABASE_SERVICE_ROLE_KEY=your-service-role-key

# ================================
# AI Provider API Keys
# ================================
OPENAI_API_KEY=sk-your-openai-api-key
ANTHROPIC_API_KEY=sk-ant-your-anthropic-api-key
GOOGLE_APPLICATION_CREDENTIALS=/path/to/google-credentials.json

# ================================
# Monitoring and Observability
# ================================
OTLP_ENDPOINT=http://jaeger:14268/api/traces
ENABLE_TRACING=true
ENABLE_METRICS=true
LOG_LEVEL=info

# ================================
# Grafana Configuration
# ================================
GRAFANA_USER=admin
GRAFANA_PASSWORD=your-secure-grafana-password

# ================================
# Redis Configuration
# ================================
REDIS_URL=redis://redis:6379
REDIS_PASSWORD=your-redis-password

# ================================
# Security Configuration
# ================================
JWT_SECRET=your-jwt-secret-key
ENCRYPTION_KEY=your-encryption-key
SESSION_SECRET=your-session-secret

# ================================
# Rate Limiting Configuration
# ================================
RATE_LIMIT_WINDOW_MS=300000
RATE_LIMIT_MAX_REQUESTS=100
RATE_LIMIT_AI_MAX_REQUESTS=20

# ================================
# Cost Management
# ================================
MAX_COST_PER_HOUR=10.00
COST_ALERT_THRESHOLD=8.00
BUDGET_LIMIT_DAILY=100.00

# ================================
# Application Configuration
# ================================
NODE_ENV=production
PORT=3001
WEB_PORT=3000

# API URLs
API_URL=https://api.your-domain.com
VITE_API_URL=https://api.your-domain.com
VITE_SUPABASE_URL=https://your-project.supabase.co
VITE_SUPABASE_ANON_KEY=your-anon-key

# ================================
# Database Configuration (if using external DB)
# ================================
DATABASE_URL=************************************/database
DATABASE_POOL_SIZE=20
DATABASE_TIMEOUT=30000

# ================================
# Email Configuration (for notifications)
# ================================
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-email-password
FROM_EMAIL=<EMAIL>

# ================================
# Notification Webhooks
# ================================
SLACK_WEBHOOK_URL=https://hooks.slack.com/services/your/slack/webhook
DISCORD_WEBHOOK_URL=https://discord.com/api/webhooks/your/discord/webhook

# ================================
# Backup Configuration
# ================================
BACKUP_ENABLED=true
BACKUP_SCHEDULE=0 2 * * *
BACKUP_RETENTION_DAYS=30
S3_BACKUP_BUCKET=your-backup-bucket
AWS_ACCESS_KEY_ID=your-aws-access-key
AWS_SECRET_ACCESS_KEY=your-aws-secret-key

# ================================
# CDN Configuration (optional)
# ================================
CDN_URL=https://cdn.your-domain.com
STATIC_ASSETS_URL=https://assets.your-domain.com

# ================================
# Analytics Configuration (optional)
# ================================
GOOGLE_ANALYTICS_ID=GA-XXXXXXXXX
MIXPANEL_TOKEN=your-mixpanel-token

# ================================
# Feature Flags
# ================================
FEATURE_AI_CACHING=true
FEATURE_ADVANCED_MONITORING=true
FEATURE_AUTO_SCALING=true
FEATURE_COST_OPTIMIZATION=true

# ================================
# Performance Configuration
# ================================
MAX_CONCURRENT_AI_REQUESTS=10
REQUEST_TIMEOUT=30000
CACHE_TTL=300
STATIC_CACHE_TTL=86400

# ================================
# Security Headers
# ================================
HSTS_MAX_AGE=31536000
CSP_REPORT_URI=https://your-domain.com/csp-report
SECURITY_HEADERS_ENABLED=true

# ================================
# Load Balancer Configuration
# ================================
LOAD_BALANCER_HEALTH_CHECK_PATH=/health
LOAD_BALANCER_TIMEOUT=10
STICKY_SESSIONS=false

# ================================
# Auto-scaling Configuration
# ================================
MIN_REPLICAS=2
MAX_REPLICAS=10
CPU_THRESHOLD=70
MEMORY_THRESHOLD=80

# ================================
# Maintenance Mode
# ================================
MAINTENANCE_MODE=false
MAINTENANCE_MESSAGE="System is under maintenance. Please try again later."

# ================================
# Legal and Compliance
# ================================
PRIVACY_POLICY_URL=https://your-domain.com/privacy
TERMS_OF_SERVICE_URL=https://your-domain.com/terms
GDPR_COMPLIANCE=true
DATA_RETENTION_DAYS=365

# ================================
# Development and Testing
# ================================
DEBUG_MODE=false
VERBOSE_LOGGING=false
ENABLE_PROFILING=false
TEST_MODE=false

# ================================
# Third-party Integrations
# ================================
GITHUB_CLIENT_ID=your-github-client-id
GITHUB_CLIENT_SECRET=your-github-client-secret
STRIPE_PUBLIC_KEY=pk_live_your-stripe-public-key
STRIPE_SECRET_KEY=sk_live_your-stripe-secret-key

# ================================
# Custom Application Settings
# ================================
MAX_FILE_UPLOAD_SIZE=10485760
ALLOWED_FILE_TYPES=.js,.ts,.jsx,.tsx,.json,.md
MAX_PATCH_SIZE=1000000
AI_TIMEOUT=60000
RETRY_ATTEMPTS=3
CIRCUIT_BREAKER_THRESHOLD=5

# ================================
# Timezone and Localization
# ================================
TZ=UTC
DEFAULT_LOCALE=en-US
SUPPORTED_LOCALES=en-US,es-ES,fr-FR,de-DE

# ================================
# Resource Limits
# ================================
MAX_MEMORY_USAGE=1024
MAX_CPU_USAGE=80
MAX_DISK_USAGE=90
MAX_NETWORK_BANDWIDTH=100

# ================================
# Logging Configuration
# ================================
LOG_FORMAT=json
LOG_ROTATION=daily
LOG_MAX_SIZE=100MB
LOG_MAX_FILES=30

# ================================
# Health Check Configuration
# ================================
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10
HEALTH_CHECK_RETRIES=3
READINESS_PROBE_DELAY=30

# ================================
# Container Configuration
# ================================
CONTAINER_MEMORY_LIMIT=1Gi
CONTAINER_CPU_LIMIT=1000m
CONTAINER_MEMORY_REQUEST=512Mi
CONTAINER_CPU_REQUEST=500m
