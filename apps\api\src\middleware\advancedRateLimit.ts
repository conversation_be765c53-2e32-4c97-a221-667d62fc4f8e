import { Request, Response, NextFunction } from 'express';
import { Redis } from 'ioredis';
import { RateLimiterRedis, RateLimiterRes } from 'rate-limiter-flexible';

// Rate limiting configuration
interface RateLimitConfig {
  windowMs: number;
  maxRequests: number;
  burstLimit?: number;
  burstWindowMs?: number;
  keyGenerator?: (req: Request) => string;
  skipSuccessfulRequests?: boolean;
  skipFailedRequests?: boolean;
  onLimitReached?: (req: Request, res: Response) => void;
}

// Default configurations for different endpoint types
export const RATE_LIMIT_CONFIGS = {
  // General API endpoints
  general: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 100,
    burstLimit: 20,
    burstWindowMs: 60 * 1000, // 1 minute
  },
  
  // AI processing endpoints (more restrictive)
  aiProcessing: {
    windowMs: 5 * 60 * 1000, // 5 minutes
    maxRequests: 20,
    burstLimit: 5,
    burstWindowMs: 60 * 1000, // 1 minute
  },
  
  // Authentication endpoints
  auth: {
    windowMs: 15 * 60 * 1000, // 15 minutes
    maxRequests: 10,
    burstLimit: 3,
    burstWindowMs: 60 * 1000, // 1 minute
  },
  
  // WebSocket upgrade requests
  websocket: {
    windowMs: 60 * 1000, // 1 minute
    maxRequests: 5,
    burstLimit: 2,
    burstWindowMs: 10 * 1000, // 10 seconds
  }
} as const;

// Redis client for rate limiting
let redisClient: Redis | null = null;

export function initializeRedis(redisUrl?: string): Redis {
  if (!redisClient) {
    redisClient = new Redis(redisUrl || process.env.REDIS_URL || 'redis://localhost:6379', {
      retryDelayOnFailover: 100,
      maxRetriesPerRequest: 3,
      lazyConnect: true,
      keyPrefix: 'rl:', // Rate limit prefix
    });

    redisClient.on('error', (error) => {
      console.error('[RateLimit] Redis connection error:', error);
    });

    redisClient.on('connect', () => {
      console.log('[RateLimit] Redis connected successfully');
    });
  }
  
  return redisClient;
}

// Generate rate limit key based on user ID or IP
function generateKey(req: Request, prefix: string): string {
  // Prefer authenticated user ID over IP address
  const userId = req.userId;
  const ip = req.ip || req.connection.remoteAddress || 'unknown';
  
  if (userId) {
    return `${prefix}:user:${userId}`;
  }
  
  return `${prefix}:ip:${ip}`;
}

// Create rate limiter instance
function createRateLimiter(config: RateLimitConfig, prefix: string): RateLimiterRedis {
  const redis = initializeRedis();
  
  return new RateLimiterRedis({
    storeClient: redis,
    keyPrefix: prefix,
    points: config.maxRequests,
    duration: Math.floor(config.windowMs / 1000), // Convert to seconds
    blockDuration: Math.floor(config.windowMs / 1000), // Block for the same duration
    execEvenly: true, // Spread requests evenly across the window
  });
}

// Create burst rate limiter for short-term limits
function createBurstLimiter(config: RateLimitConfig, prefix: string): RateLimiterRedis | null {
  if (!config.burstLimit || !config.burstWindowMs) {
    return null;
  }
  
  const redis = initializeRedis();
  
  return new RateLimiterRedis({
    storeClient: redis,
    keyPrefix: `${prefix}:burst`,
    points: config.burstLimit,
    duration: Math.floor(config.burstWindowMs / 1000),
    blockDuration: Math.floor(config.burstWindowMs / 1000),
    execEvenly: false, // Allow bursts
  });
}

// Advanced rate limiting middleware factory
export function createAdvancedRateLimit(
  configName: keyof typeof RATE_LIMIT_CONFIGS,
  customConfig?: Partial<RateLimitConfig>
) {
  const baseConfig = RATE_LIMIT_CONFIGS[configName];
  const config: RateLimitConfig = { ...baseConfig, ...customConfig };
  
  const rateLimiter = createRateLimiter(config, configName);
  const burstLimiter = createBurstLimiter(config, configName);
  
  return async (req: Request, res: Response, next: NextFunction) => {
    try {
      const key = config.keyGenerator ? config.keyGenerator(req) : generateKey(req, configName);
      
      // Check burst limit first (if configured)
      if (burstLimiter) {
        try {
          await burstLimiter.consume(key);
        } catch (burstResult) {
          if (burstResult instanceof RateLimiterRes) {
            return handleRateLimit(req, res, burstResult, 'burst', config);
          }
          throw burstResult;
        }
      }
      
      // Check main rate limit
      try {
        const result = await rateLimiter.consume(key);
        
        // Add rate limit headers
        addRateLimitHeaders(res, result, config);
        
        next();
      } catch (rateLimitResult) {
        if (rateLimitResult instanceof RateLimiterRes) {
          return handleRateLimit(req, res, rateLimitResult, 'main', config);
        }
        throw rateLimitResult;
      }
      
    } catch (error) {
      console.error('[RateLimit] Error in rate limiting middleware:', error);
      
      // Fail open - allow request if Redis is down
      next();
    }
  };
}

// Handle rate limit exceeded
function handleRateLimit(
  req: Request,
  res: Response,
  rateLimitResult: RateLimiterRes,
  limitType: 'main' | 'burst',
  config: RateLimitConfig
) {
  const retryAfter = Math.round(rateLimitResult.msBeforeNext / 1000);
  
  // Add rate limit headers
  res.set({
    'Retry-After': retryAfter.toString(),
    'X-RateLimit-Limit': config.maxRequests.toString(),
    'X-RateLimit-Remaining': '0',
    'X-RateLimit-Reset': new Date(Date.now() + rateLimitResult.msBeforeNext).toISOString(),
    'X-RateLimit-Type': limitType,
  });
  
  // Call custom handler if provided
  if (config.onLimitReached) {
    config.onLimitReached(req, res);
  }
  
  // Log rate limit hit
  console.warn(`[RateLimit] ${limitType} limit exceeded for ${req.ip || 'unknown'} on ${req.path}`);
  
  res.status(429).json({
    error: 'Too Many Requests',
    message: `Rate limit exceeded. Try again in ${retryAfter} seconds.`,
    retryAfter,
    limitType,
  });
}

// Add rate limit headers to successful responses
function addRateLimitHeaders(res: Response, result: RateLimiterRes, config: RateLimitConfig) {
  res.set({
    'X-RateLimit-Limit': config.maxRequests.toString(),
    'X-RateLimit-Remaining': result.remainingPoints?.toString() || '0',
    'X-RateLimit-Reset': new Date(Date.now() + result.msBeforeNext).toISOString(),
  });
}

// WebSocket rate limiting
export function createWebSocketRateLimit() {
  const config = RATE_LIMIT_CONFIGS.websocket;
  const rateLimiter = createRateLimiter(config, 'websocket');
  
  return async (req: Request): Promise<boolean> => {
    try {
      const key = generateKey(req, 'websocket');
      await rateLimiter.consume(key);
      return true;
    } catch (rateLimitResult) {
      if (rateLimitResult instanceof RateLimiterRes) {
        console.warn(`[RateLimit] WebSocket upgrade denied for ${req.ip || 'unknown'}`);
        return false;
      }
      
      // Fail open on errors
      console.error('[RateLimit] Error in WebSocket rate limiting:', rateLimitResult);
      return true;
    }
  };
}

// Rate limit status endpoint
export function getRateLimitStatus(req: Request, res: Response) {
  const key = generateKey(req, 'general');
  const redis = initializeRedis();
  
  // Get current rate limit status for the user/IP
  redis.multi()
    .get(`general:${key}`)
    .get(`aiProcessing:${key}`)
    .get(`auth:${key}`)
    .exec((err, results) => {
      if (err) {
        return res.status(500).json({ error: 'Failed to get rate limit status' });
      }
      
      const [generalResult, aiResult, authResult] = results || [];
      
      res.json({
        general: {
          remaining: generalResult?.[1] ? RATE_LIMIT_CONFIGS.general.maxRequests - parseInt(generalResult[1] as string) : RATE_LIMIT_CONFIGS.general.maxRequests,
          limit: RATE_LIMIT_CONFIGS.general.maxRequests,
          window: RATE_LIMIT_CONFIGS.general.windowMs,
        },
        aiProcessing: {
          remaining: aiResult?.[1] ? RATE_LIMIT_CONFIGS.aiProcessing.maxRequests - parseInt(aiResult[1] as string) : RATE_LIMIT_CONFIGS.aiProcessing.maxRequests,
          limit: RATE_LIMIT_CONFIGS.aiProcessing.maxRequests,
          window: RATE_LIMIT_CONFIGS.aiProcessing.windowMs,
        },
        auth: {
          remaining: authResult?.[1] ? RATE_LIMIT_CONFIGS.auth.maxRequests - parseInt(authResult[1] as string) : RATE_LIMIT_CONFIGS.auth.maxRequests,
          limit: RATE_LIMIT_CONFIGS.auth.maxRequests,
          window: RATE_LIMIT_CONFIGS.auth.windowMs,
        },
      });
    });
}

// Cleanup function
export function cleanup() {
  if (redisClient) {
    redisClient.disconnect();
    redisClient = null;
  }
}
