# Prometheus Configuration for Metamorphic Reactor
# Comprehensive monitoring setup with service discovery and alerting

global:
  scrape_interval: 15s
  evaluation_interval: 15s
  external_labels:
    cluster: 'metamorphic-reactor'
    environment: 'production'

# Alertmanager configuration
alerting:
  alertmanagers:
    - static_configs:
        - targets:
          - alertmanager:9093

# Load rules once and periodically evaluate them
rule_files:
  - "alert_rules.yml"
  - "recording_rules.yml"

# Scrape configurations
scrape_configs:
  # Prometheus itself
  - job_name: 'prometheus'
    static_configs:
      - targets: ['localhost:9090']
    scrape_interval: 30s
    metrics_path: /metrics

  # Metamorphic Reactor API
  - job_name: 'metamorphic-api'
    static_configs:
      - targets: ['api:3001']
    scrape_interval: 15s
    metrics_path: /api/monitoring/metrics
    scrape_timeout: 10s
    honor_labels: true
    params:
      format: ['prometheus']

  # Metamorphic Reactor Web
  - job_name: 'metamorphic-web'
    static_configs:
      - targets: ['web:3000']
    scrape_interval: 30s
    metrics_path: /metrics
    scrape_timeout: 10s

  # Redis metrics
  - job_name: 'redis'
    static_configs:
      - targets: ['redis:6379']
    scrape_interval: 30s
    metrics_path: /metrics

  # Traefik metrics
  - job_name: 'traefik'
    static_configs:
      - targets: ['traefik:8080']
    scrape_interval: 30s
    metrics_path: /metrics

  # Node Exporter (if available)
  - job_name: 'node-exporter'
    static_configs:
      - targets: ['node-exporter:9100']
    scrape_interval: 30s
    honor_labels: true

  # Jaeger metrics
  - job_name: 'jaeger'
    static_configs:
      - targets: ['jaeger:14269']
    scrape_interval: 30s
    metrics_path: /metrics

  # Grafana metrics
  - job_name: 'grafana'
    static_configs:
      - targets: ['grafana:3000']
    scrape_interval: 60s
    metrics_path: /metrics

  # Custom application metrics
  - job_name: 'ai-agents'
    static_configs:
      - targets: ['api:3001']
    scrape_interval: 15s
    metrics_path: /api/monitoring/ai-metrics
    params:
      format: ['prometheus']
    metric_relabel_configs:
      - source_labels: [__name__]
        regex: 'ai_.*'
        target_label: component
        replacement: 'ai-agent'

  # Cache metrics
  - job_name: 'cache-metrics'
    static_configs:
      - targets: ['api:3001']
    scrape_interval: 30s
    metrics_path: /api/monitoring/cache-stats
    params:
      format: ['prometheus']

  # Rate limiting metrics
  - job_name: 'rate-limit-metrics'
    static_configs:
      - targets: ['api:3001']
    scrape_interval: 30s
    metrics_path: /api/rate-limit-status
    params:
      format: ['prometheus']

# Remote write configuration (for long-term storage)
remote_write:
  - url: "http://prometheus-remote-storage:9201/write"
    queue_config:
      max_samples_per_send: 1000
      max_shards: 200
      capacity: 2500
    write_relabel_configs:
      - source_labels: [__name__]
        regex: 'go_.*|process_.*|prometheus_.*'
        action: drop

# Storage configuration
storage:
  tsdb:
    retention.time: 30d
    retention.size: 10GB
    wal-compression: true

# Recording rules for performance optimization
recording_rules:
  - name: metamorphic_reactor_rules
    interval: 30s
    rules:
      # API request rate
      - record: metamorphic:api_request_rate
        expr: rate(http_requests_total[5m])
        labels:
          service: api

      # API error rate
      - record: metamorphic:api_error_rate
        expr: rate(http_requests_total{status=~"5.."}[5m]) / rate(http_requests_total[5m])
        labels:
          service: api

      # AI operation success rate
      - record: metamorphic:ai_success_rate
        expr: rate(ai_operations_total{status="success"}[5m]) / rate(ai_operations_total[5m])
        labels:
          component: ai-agent

      # Cache hit rate
      - record: metamorphic:cache_hit_rate
        expr: rate(cache_hits_total[5m]) / (rate(cache_hits_total[5m]) + rate(cache_misses_total[5m]))
        labels:
          component: cache

      # Memory usage percentage
      - record: metamorphic:memory_usage_percent
        expr: (process_resident_memory_bytes / node_memory_MemTotal_bytes) * 100
        labels:
          component: system

# Alert rules
alert_rules:
  - name: metamorphic_reactor_alerts
    rules:
      # High error rate
      - alert: HighErrorRate
        expr: metamorphic:api_error_rate > 0.1
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High error rate detected"
          description: "API error rate is {{ $value | humanizePercentage }} for the last 5 minutes"

      # High response time
      - alert: HighResponseTime
        expr: histogram_quantile(0.95, rate(http_request_duration_seconds_bucket[5m])) > 2
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High response time detected"
          description: "95th percentile response time is {{ $value }}s"

      # Low AI success rate
      - alert: LowAISuccessRate
        expr: metamorphic:ai_success_rate < 0.9
        for: 10m
        labels:
          severity: critical
        annotations:
          summary: "Low AI operation success rate"
          description: "AI success rate is {{ $value | humanizePercentage }}"

      # High memory usage
      - alert: HighMemoryUsage
        expr: metamorphic:memory_usage_percent > 90
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High memory usage"
          description: "Memory usage is {{ $value }}%"

      # Service down
      - alert: ServiceDown
        expr: up == 0
        for: 1m
        labels:
          severity: critical
        annotations:
          summary: "Service is down"
          description: "{{ $labels.job }} service is down"

      # High AI costs
      - alert: HighAICosts
        expr: increase(ai_cost_total[1h]) > 10
        for: 0m
        labels:
          severity: warning
        annotations:
          summary: "High AI costs detected"
          description: "AI costs increased by ${{ $value }} in the last hour"

      # Cache performance degradation
      - alert: LowCacheHitRate
        expr: metamorphic:cache_hit_rate < 0.5
        for: 10m
        labels:
          severity: warning
        annotations:
          summary: "Low cache hit rate"
          description: "Cache hit rate is {{ $value | humanizePercentage }}"

      # Rate limiting triggered frequently
      - alert: HighRateLimitHits
        expr: rate(rate_limit_hits_total[5m]) > 10
        for: 5m
        labels:
          severity: warning
        annotations:
          summary: "High rate limit hits"
          description: "Rate limiting is being triggered {{ $value }} times per second"
