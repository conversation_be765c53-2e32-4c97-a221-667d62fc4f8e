import { Router } from 'express';
import { repositoryMonitor } from '../services/repositoryMonitor.js';
import { loggingService } from '../services/loggingService.js';
import { supabaseClient } from '../services/supabase.js';
import { requireAuth } from '../middleware/auth.js';

const router = Router();

/**
 * GET /api/repositories
 * Get all repositories for the authenticated user
 */
router.get('/repositories', requireAuth, async (req, res) => {
  try {
    const user = req.user;

    // Get repositories from database
    const { data: repositories, error } = await supabaseClient
      .from('autonomous_repositories')
      .select(`
        *,
        repository_stats!inner(
          total_scans,
          successful_scans,
          total_opportunities,
          completed_improvements,
          total_cost,
          avg_opportunities_per_scan,
          last_successful_scan
        )
      `)
      .eq('user_id', user.id)
      .order('created_at', { ascending: false });

    if (error) {
      throw error;
    }

    res.json({
      repositories: repositories || [],
      monitoringStatus: repositoryMonitor.getMonitoringStatus()
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get repositories',
      service: 'repositories-api',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /repositories'
      }
    });

    res.status(500).json({
      error: 'Failed to get repositories',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/repositories
 * Add a new repository for autonomous monitoring
 */
router.post('/repositories', requireAuth, async (req, res) => {
  try {
    const user = req.user;

    const {
      owner,
      name,
      branch = 'main',
      enabled = true,
      scanInterval = 60,
      qualityThreshold = 0.8,
      costLimit = 3.0,
      improvementTargets = [],
      excludePatterns = ['node_modules/**', '.git/**', 'dist/**']
    } = req.body;

    // Validate required fields
    if (!owner || !name) {
      return res.status(400).json({
        error: 'Missing required fields',
        details: 'owner and name are required'
      });
    }

    // Validate cost limit
    if (costLimit > 10.0) {
      return res.status(400).json({
        error: 'Cost limit too high',
        details: 'Maximum cost limit is $10.00 per repository'
      });
    }

    // Check if repository already exists for this user
    const { data: existing } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('owner', owner)
      .eq('name', name)
      .eq('user_id', user.id)
      .single();

    if (existing) {
      return res.status(409).json({
        error: 'Repository already exists',
        details: `Repository ${owner}/${name} is already being monitored`
      });
    }

    // Add repository to monitoring
    const repositoryId = await repositoryMonitor.addRepository({
      owner,
      name,
      branch,
      enabled,
      scanInterval,
      qualityThreshold,
      costLimit,
      improvementTargets,
      excludePatterns
    });

    // Update database record with user_id
    const { error: updateError } = await supabaseClient
      .from('autonomous_repositories')
      .update({ user_id: user.id })
      .eq('id', repositoryId);

    if (updateError) {
      throw updateError;
    }

    await loggingService.log({
      level: 'info',
      message: 'Repository added for autonomous monitoring',
      service: 'repositories-api',
      metadata: {
        repositoryId,
        owner,
        name,
        userId: user.id,
        enabled,
        costLimit
      }
    });

    res.status(201).json({
      message: 'Repository added successfully',
      repositoryId,
      repository: repositoryMonitor.getRepository(repositoryId)
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to add repository',
      service: 'repositories-api',
      metadata: {
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'POST /repositories',
        body: req.body
      }
    });

    res.status(500).json({
      error: 'Failed to add repository',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * PUT /api/repositories/:id
 * Update repository configuration
 */
router.put('/repositories/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found' });
    }

    const updates = req.body;

    // Validate cost limit if provided
    if (updates.costLimit && updates.costLimit > 10.0) {
      return res.status(400).json({
        error: 'Cost limit too high',
        details: 'Maximum cost limit is $10.00 per repository'
      });
    }

    // Update repository
    await repositoryMonitor.updateRepository(id, updates);

    await loggingService.log({
      level: 'info',
      message: 'Repository configuration updated',
      service: 'repositories-api',
      metadata: {
        repositoryId: id,
        userId: user.id,
        updates
      }
    });

    res.json({
      message: 'Repository updated successfully',
      repository: repositoryMonitor.getRepository(id)
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to update repository',
      service: 'repositories-api',
      metadata: {
        repositoryId: req.params.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'PUT /repositories/:id'
      }
    });

    res.status(500).json({
      error: 'Failed to update repository',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * DELETE /api/repositories/:id
 * Remove repository from monitoring
 */
router.delete('/repositories/:id', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found' });
    }

    // Remove repository
    await repositoryMonitor.removeRepository(id);

    await loggingService.log({
      level: 'info',
      message: 'Repository removed from monitoring',
      service: 'repositories-api',
      metadata: {
        repositoryId: id,
        userId: user.id,
        owner: repository.owner,
        name: repository.name
      }
    });

    res.json({
      message: 'Repository removed successfully'
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to remove repository',
      service: 'repositories-api',
      metadata: {
        repositoryId: req.params.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'DELETE /repositories/:id'
      }
    });

    res.status(500).json({
      error: 'Failed to remove repository',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * POST /api/repositories/:id/scan
 * Trigger manual scan for a repository
 */
router.post('/repositories/:id/scan', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('*')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found' });
    }

    // Trigger scan
    const scanResult = await repositoryMonitor.scanRepository(id);

    await loggingService.log({
      level: 'info',
      message: 'Manual repository scan triggered',
      service: 'repositories-api',
      metadata: {
        repositoryId: id,
        scanId: scanResult.scanId,
        userId: user.id
      }
    });

    res.json({
      message: 'Scan started successfully',
      scanId: scanResult.scanId,
      status: scanResult.status
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to trigger repository scan',
      service: 'repositories-api',
      metadata: {
        repositoryId: req.params.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'POST /repositories/:id/scan'
      }
    });

    res.status(500).json({
      error: 'Failed to trigger scan',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/repositories/:id/scans
 * Get scan history for a repository
 */
router.get('/repositories/:id/scans', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const user = req.user;

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found' });
    }

    // Get scan history from database
    const { data: scans, error: scansError } = await supabaseClient
      .from('repository_scans')
      .select('*')
      .eq('repository_id', id)
      .order('started_at', { ascending: false })
      .limit(50);

    if (scansError) {
      throw scansError;
    }

    // Also get in-memory scan history for recent scans
    const memoryHistory = repositoryMonitor.getScanHistory(id);

    res.json({
      scans: scans || [],
      recentScans: memoryHistory
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get scan history',
      service: 'repositories-api',
      metadata: {
        repositoryId: req.params.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /repositories/:id/scans'
      }
    });

    res.status(500).json({
      error: 'Failed to get scan history',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

/**
 * GET /api/repositories/:id/opportunities
 * Get improvement opportunities for a repository
 */
router.get('/repositories/:id/opportunities', requireAuth, async (req, res) => {
  try {
    const { id } = req.params;
    const { status, type, priority } = req.query;
    const user = req.user;

    // Verify repository ownership
    const { data: repository, error: repoError } = await supabaseClient
      .from('autonomous_repositories')
      .select('id')
      .eq('id', id)
      .eq('user_id', user.id)
      .single();

    if (repoError || !repository) {
      return res.status(404).json({ error: 'Repository not found' });
    }

    // Build query
    let query = supabaseClient
      .from('improvement_opportunities')
      .select('*')
      .eq('repository_id', id);

    // Apply filters
    if (status) {
      query = query.eq('status', status);
    }
    if (type) {
      query = query.eq('type', type);
    }
    if (priority) {
      query = query.gte('priority', parseInt(priority as string));
    }

    const { data: opportunities, error: oppError } = await query
      .order('priority', { ascending: false })
      .order('detected_at', { ascending: false })
      .limit(100);

    if (oppError) {
      throw oppError;
    }

    // Get summary statistics
    const { data: stats, error: statsError } = await supabaseClient
      .from('improvement_opportunities')
      .select('type, status, priority, estimated_cost')
      .eq('repository_id', id);

    if (statsError) {
      throw statsError;
    }

    const summary = {
      total: stats?.length || 0,
      byType: {},
      byStatus: {},
      byPriority: { high: 0, medium: 0, low: 0 },
      totalEstimatedCost: stats?.reduce((sum, opp) => sum + parseFloat(opp.estimated_cost), 0) || 0
    };

    // Calculate summaries
    stats?.forEach(opp => {
      // By type
      summary.byType[opp.type] = (summary.byType[opp.type] || 0) + 1;

      // By status
      summary.byStatus[opp.status] = (summary.byStatus[opp.status] || 0) + 1;

      // By priority
      if (opp.priority >= 8) summary.byPriority.high++;
      else if (opp.priority >= 5) summary.byPriority.medium++;
      else summary.byPriority.low++;
    });

    res.json({
      opportunities: opportunities || [],
      summary
    });

  } catch (error) {
    await loggingService.log({
      level: 'error',
      message: 'Failed to get improvement opportunities',
      service: 'repositories-api',
      metadata: {
        repositoryId: req.params.id,
        error: error instanceof Error ? error.message : 'Unknown error',
        endpoint: 'GET /repositories/:id/opportunities'
      }
    });

    res.status(500).json({
      error: 'Failed to get improvement opportunities',
      details: error instanceof Error ? error.message : 'Unknown error'
    });
  }
});

export { router as repositoriesRouter };
