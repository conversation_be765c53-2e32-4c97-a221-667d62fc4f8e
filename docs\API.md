# 🔮 Metamorphic Reactor API Documentation

## Overview

The Metamorphic Reactor API provides endpoints for running dual-agent code transformations, managing user settings, and integrating with GitHub. All endpoints require proper authentication and follow RESTful conventions.

## Base URL

- **Development**: `http://localhost:3001`
- **Production**: `https://your-api-domain.com`

## Authentication

The API uses Supabase authentication with JWT tokens. All protected endpoints require a valid JWT token in the Authorization header.

### Authentication Methods

#### User Authentication (Standard Endpoints)
```http
Authorization: Bearer <supabase-jwt-token>
```

#### Service Role Authentication (Admin Endpoints)
```http
Authorization: Bearer <supabase-service-role-key>
```

### JWT Token Structure
JWT tokens contain the following claims:
- `sub`: User ID
- `email`: User email address
- `role`: User role (`authenticated`, `admin`)
- `aud`: Audience (`authenticated`)
- `iss`: Issuer (Supabase URL)
- `exp`: Expiration timestamp
- `app_metadata`: Application metadata (includes role)
- `user_metadata`: User profile data

### Authentication Errors
| Status | Code | Description |
|--------|------|-------------|
| 401 | `MISSING_AUTH_HEADER` | Authorization header not provided |
| 401 | `INVALID_AUTH_FORMAT` | Authorization header format invalid |
| 401 | `MISSING_JWT_TOKEN` | JWT token missing from Bearer header |
| 401 | `EXPIRED_TOKEN` | JWT token has expired |
| 401 | `INVALID_TOKEN` | JWT token is invalid or malformed |
| 401 | `USER_NOT_FOUND` | User associated with token not found |
| 403 | `INSUFFICIENT_PERMISSIONS` | User lacks required permissions |
| 500 | `AUTH_SERVICE_ERROR` | Authentication service error |

### Endpoint Authentication Requirements

#### Public Endpoints (No Authentication)
- `GET /api/health` - Health check
- `GET /api/billing/plans` - Available subscription plans

#### User Authentication Required
- All `/api/onboarding/*` endpoints
- All `/api/billing/*` endpoints (except plans)
- All `/api/settings/*` endpoints
- All `/api/notifications/*` endpoints
- All `/api/repositories/*` endpoints
- All `/api/auto-pr/*` endpoints

#### Admin Authentication Required
- All `/api/secrets/*` endpoints (service role key)
- `GET /api/queue/detailed` (service role key)
- `POST /api/queue/clear` (service role key)

## Rate Limiting

- **Default**: 60 requests per minute per user
- **Burst**: Up to 100 requests in a 10-second window
- **Headers**: Rate limit info included in response headers

## Error Handling

All errors follow this format:

```json
{
  "error": "Error type",
  "message": "Human-readable error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "Additional error details"
  }
}
```

## Endpoints

### 🤖 Reactor Loop

#### Start Reactor Loop
```http
POST /api/reactor/loop
```

**Request Body:**
```json
{
  "prompt": "Add error handling to this function",
  "maxIterations": 10,
  "scoreThreshold": 0.95,
  "plannerModel": "gpt-4-turbo",
  "criticModel": "claude-3-sonnet"
}
```

**Response:**
```json
{
  "sessionId": "uuid-session-id",
  "status": "running",
  "createdAt": "2024-01-01T00:00:00Z",
  "estimatedCost": 0.15
}
```

#### Start Streaming Loop
```http
POST /api/reactor/loop/stream
```

**Request Body:** Same as above

**Response:** Server-Sent Events (SSE) stream with:
```json
{
  "type": "iteration",
  "data": {
    "iteration": 1,
    "plan": "Generated patch",
    "critique": "Evaluation feedback",
    "score": 0.85,
    "cost": 0.02
  }
}
```

#### Get Session Status
```http
GET /api/reactor/loop/:sessionId
```

**Response:**
```json
{
  "sessionId": "uuid",
  "status": "completed",
  "iterations": 5,
  "finalScore": 0.96,
  "totalCost": 0.08,
  "finalPatch": {
    "operations": [...],
    "description": "Final transformation",
    "confidence": 0.96
  },
  "createdAt": "2024-01-01T00:00:00Z",
  "completedAt": "2024-01-01T00:01:30Z"
}
```

#### Stop Running Loop
```http
POST /api/reactor/loop/:sessionId/stop
```

**Response:**
```json
{
  "sessionId": "uuid",
  "status": "stopped",
  "message": "Loop stopped successfully"
}
```

### 📊 Session Management

#### List User Sessions
```http
GET /api/reactor/sessions?page=1&limit=20&status=completed
```

**Query Parameters:**
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 20, max: 100)
- `status`: Filter by status (running, completed, failed, stopped)
- `search`: Search in prompts and descriptions

**Response:**
```json
{
  "sessions": [...],
  "pagination": {
    "page": 1,
    "limit": 20,
    "total": 150,
    "pages": 8
  }
}
```

#### Delete Session
```http
DELETE /api/reactor/sessions/:sessionId
```

**Response:**
```json
{
  "message": "Session deleted successfully"
}
```

### 🔧 Settings

#### Get User Settings
```http
GET /api/settings
```

**Response:**
```json
{
  "plannerModel": "gpt-4-turbo",
  "criticModel": "claude-3-sonnet",
  "maxIterations": 10,
  "scoreThreshold": 0.95,
  "maxCostUsd": 3.0,
  "timeoutSeconds": 180,
  "telemetryEnabled": true,
  "apiKeys": {
    "openai": "sk-...***",
    "anthropic": "sk-ant-...***"
  }
}
```

#### Update Settings
```http
PUT /api/settings
```

**Request Body:**
```json
{
  "plannerModel": "claude-3-sonnet",
  "maxIterations": 15,
  "scoreThreshold": 0.98,
  "apiKeys": {
    "openai": "sk-new-key",
    "anthropic": "sk-ant-new-key"
  }
}
```

**Response:**
```json
{
  "message": "Settings updated successfully",
  "settings": { ... }
}
```

### 🔐 GitHub Integration

#### GitHub OAuth Callback
```http
POST /api/auth/github/callback
```

**Request Body:**
```json
{
  "code": "github-oauth-code",
  "state": "csrf-state-token"
}
```

**Response:**
```json
{
  "accessToken": "gho_...",
  "user": {
    "id": 123,
    "login": "username",
    "name": "User Name",
    "email": "<EMAIL>"
  }
}
```

#### Create Pull Request
```http
POST /api/github/pr
```

**Request Body:**
```json
{
  "sessionId": "uuid-session-id",
  "owner": "username",
  "repo": "repository",
  "title": "Custom PR title",
  "description": "Custom description",
  "baseBranch": "main"
}
```

**Response:**
```json
{
  "prNumber": 123,
  "htmlUrl": "https://github.com/owner/repo/pull/123",
  "branchName": "reactor/2024-01-01-12-30-45",
  "status": "draft"
}
```

#### List User Repositories
```http
GET /api/github/repos
```

**Response:**
```json
{
  "repositories": [
    {
      "id": 123,
      "name": "my-repo",
      "fullName": "username/my-repo",
      "defaultBranch": "main",
      "permissions": {
        "admin": true,
        "push": true,
        "pull": true
      }
    }
  ]
}
```

### 📈 Analytics

#### Get Usage Statistics
```http
GET /api/analytics/usage?period=30d
```

**Query Parameters:**
- `period`: Time period (7d, 30d, 90d, 1y)

**Response:**
```json
{
  "period": "30d",
  "totalSessions": 45,
  "successfulSessions": 42,
  "averageScore": 0.94,
  "totalCost": 12.50,
  "averageCost": 0.28,
  "averageIterations": 6.2,
  "topFeatures": [
    { "feature": "reactor_loop", "count": 45 },
    { "feature": "github_pr", "count": 23 }
  ]
}
```

#### Export User Data
```http
GET /api/analytics/export
```

**Response:** JSON file download with all user data

### 🔍 Health & Monitoring

#### Health Check
```http
GET /api/health
```

**Response:**
```json
{
  "status": "healthy",
  "timestamp": "2024-01-01T00:00:00Z",
  "version": "1.0.0",
  "services": {
    "database": "healthy",
    "openai": "healthy",
    "anthropic": "healthy",
    "github": "healthy"
  }
}
```

#### API Status
```http
GET /api/status
```

**Response:**
```json
{
  "api": {
    "version": "1.0.0",
    "uptime": 86400,
    "requestsToday": 1250
  },
  "limits": {
    "rateLimit": "60/minute",
    "maxCost": "$3.00",
    "maxTimeout": "180s"
  }
}
```

## WebSocket Events

### Real-time Loop Updates

Connect to: `ws://localhost:3001/ws/reactor/:sessionId`

**Events:**
```json
{
  "type": "iteration_start",
  "data": { "iteration": 1 }
}

{
  "type": "plan_generated",
  "data": {
    "iteration": 1,
    "patch": { ... },
    "tokens": 150,
    "cost": 0.02
  }
}

{
  "type": "critique_received",
  "data": {
    "iteration": 1,
    "score": 0.85,
    "feedback": "Good patch, but needs error handling",
    "tokens": 75,
    "cost": 0.01
  }
}

{
  "type": "loop_completed",
  "data": {
    "finalScore": 0.96,
    "totalIterations": 5,
    "totalCost": 0.08,
    "finalPatch": { ... }
  }
}

{
  "type": "error",
  "data": {
    "message": "API rate limit exceeded",
    "code": "RATE_LIMIT_ERROR"
  }
}
```

## Error Codes

### Authentication Errors (401)
| Code | Description |
|------|-------------|
| `MISSING_AUTH_HEADER` | Authorization header not provided |
| `INVALID_AUTH_FORMAT` | Authorization header format invalid (must be "Bearer <token>") |
| `MISSING_JWT_TOKEN` | JWT token missing from Bearer header |
| `EXPIRED_TOKEN` | JWT token has expired |
| `INVALID_TOKEN` | JWT token is invalid or malformed |
| `INVALID_TOKEN_FORMAT` | JWT token format or signature invalid |
| `USER_NOT_FOUND` | User associated with token not found |
| `UNAUTHENTICATED` | User must be authenticated before checking permissions |

### Authorization Errors (403)
| Code | Description |
|------|-------------|
| `INSUFFICIENT_PERMISSIONS` | User lacks required permissions for this operation |
| `INVALID_SERVICE_KEY` | Service role key does not match |
| `MISSING_SERVICE_AUTH` | Service role authorization required |

### General API Errors
| Code | Description |
|------|-------------|
| `INVALID_REQUEST` | Malformed request body or parameters |
| `NOT_FOUND` | Resource not found |
| `RATE_LIMIT_ERROR` | Too many requests |
| `COST_LIMIT_ERROR` | User cost limit exceeded |
| `TIMEOUT_ERROR` | Request timeout |
| `AI_API_ERROR` | OpenAI/Anthropic API error |
| `GITHUB_ERROR` | GitHub API error |
| `DATABASE_ERROR` | Database connection error |
| `VALIDATION_ERROR` | Input validation failed |
| `AUTH_SERVICE_ERROR` | Authentication service error |
| `SERVICE_CONFIG_ERROR` | Service configuration error |

## SDKs and Examples

### JavaScript/TypeScript
```typescript
import { ReactorClient } from '@metamorphic/reactor-sdk';

const client = new ReactorClient({
  apiUrl: 'http://localhost:3001',
  token: 'your-supabase-jwt'
});

// Start a reactor loop
const session = await client.startLoop({
  prompt: 'Add error handling',
  maxIterations: 10
});

// Stream updates
client.streamLoop(session.sessionId, (event) => {
  console.log('Update:', event);
});
```

### cURL Examples
```bash
# Start reactor loop
curl -X POST http://localhost:3001/api/reactor/loop \
  -H "Authorization: Bearer your-token" \
  -H "Content-Type: application/json" \
  -d '{"prompt": "Add error handling", "maxIterations": 10}'

# Get session status
curl -X GET http://localhost:3001/api/reactor/loop/session-id \
  -H "Authorization: Bearer your-token"
```

## Rate Limits & Costs

### Default Limits
- **API Requests**: 60/minute per user
- **Concurrent Loops**: 3 per user
- **Cost Limit**: $3.00 per user per day
- **Timeout**: 180 seconds per loop

### Cost Estimation
- **GPT-4 Turbo**: ~$0.03 per 1K tokens
- **Claude 3 Sonnet**: ~$0.008 per 1K tokens
- **Typical Loop**: $0.01-0.10 depending on complexity
- **Average**: ~$0.05 per successful transformation

## Support

- **Documentation**: [docs.metamorphic-reactor.com](https://docs.metamorphic-reactor.com)
- **GitHub Issues**: [github.com/your-org/metamorphic-reactor/issues](https://github.com/your-org/metamorphic-reactor/issues)
- **Discord**: [discord.gg/metamorphic-reactor](https://discord.gg/metamorphic-reactor)
- **Email**: <EMAIL>
