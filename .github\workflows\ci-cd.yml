name: CI/CD Pipeline

on:
  push:
    branches: [ main, develop, feat/*, fix/* ]
  pull_request:
    branches: [ main, develop ]
  workflow_dispatch:
    inputs:
      deploy_environment:
        description: 'Environment to deploy to'
        required: true
        default: 'staging'
        type: choice
        options:
          - staging
          - production

env:
  NODE_VERSION: '18'
  REGISTRY: ghcr.io
  IMAGE_NAME: ${{ github.repository }}

jobs:
  # Job 1: Code Quality and Linting
  code-quality:
    name: Code Quality & Linting
    runs-on: ubuntu-latest
    timeout-minutes: 10
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0 # Needed for SonarQube
          
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run ESLint
        run: npm run lint
        continue-on-error: true
        
      - name: Run Prettier check
        run: npm run format:check
        continue-on-error: true
        
      - name: Run TypeScript check
        run: npm run type-check
        
      - name: Upload ESLint results
        uses: github/super-linter@v5
        if: always()
        env:
          DEFAULT_BRANCH: main
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}
          VALIDATE_TYPESCRIPT_ES: true
          VALIDATE_JAVASCRIPT_ES: true
          VALIDATE_CSS: true
          VALIDATE_HTML: true

  # Job 2: Security Scanning
  security-scan:
    name: Security Scanning
    runs-on: ubuntu-latest
    timeout-minutes: 15
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Run npm audit
        run: npm audit --audit-level=high
        continue-on-error: true
        
      - name: Run Snyk security scan
        uses: snyk/actions/node@master
        if: github.event_name == 'pull_request'
        env:
          SNYK_TOKEN: ${{ secrets.SNYK_TOKEN }}
        with:
          args: --severity-threshold=high
          
      - name: CodeQL Analysis
        uses: github/codeql-action/init@v3
        with:
          languages: javascript,typescript
          
      - name: Perform CodeQL Analysis
        uses: github/codeql-action/analyze@v3

  # Job 3: Build and Test Matrix
  build-test:
    name: Build & Test
    runs-on: ${{ matrix.os }}
    timeout-minutes: 20
    
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
        node-version: ['18', '20']
        include:
          - os: ubuntu-latest
            node-version: '18'
            coverage: true
            
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js ${{ matrix.node-version }}
        uses: actions/setup-node@v4
        with:
          node-version: ${{ matrix.node-version }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build packages
        run: npm run build
        
      - name: Run unit tests
        run: npm test
        env:
          CI: true
          
      - name: Run integration tests
        run: npm run test:integration
        if: matrix.os == 'ubuntu-latest'
        env:
          CI: true
          
      - name: Generate coverage report
        if: matrix.coverage
        run: npm run test:coverage
        
      - name: Upload coverage to Codecov
        if: matrix.coverage
        uses: codecov/codecov-action@v4
        with:
          token: ${{ secrets.CODECOV_TOKEN }}
          files: ./coverage/lcov.info
          flags: unittests
          name: codecov-umbrella
          
      - name: Upload test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: test-results-${{ matrix.os }}-${{ matrix.node-version }}
          path: |
            test-results.xml
            coverage/
          retention-days: 30

  # Job 4: End-to-End Testing
  e2e-tests:
    name: E2E Tests
    runs-on: ubuntu-latest
    timeout-minutes: 30
    needs: [build-test]
    
    services:
      postgres:
        image: postgres:15
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: test_db
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
          
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build applications
        run: npm run build
        
      - name: Start API server
        run: |
          cd apps/api
          npm start &
          sleep 10
        env:
          NODE_ENV: test
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/test_db
          
      - name: Start frontend server
        run: |
          cd apps/web
          npm run preview &
          sleep 10
          
      - name: Run E2E tests
        run: npm run test:e2e
        env:
          CI: true
          
      - name: Upload E2E test results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: e2e-test-results
          path: |
            e2e-test-results/
            screenshots/
          retention-days: 30

  # Job 5: Performance Testing
  performance-test:
    name: Performance Testing
    runs-on: ubuntu-latest
    timeout-minutes: 20
    needs: [build-test]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Setup Node.js
        uses: actions/setup-node@v4
        with:
          node-version: ${{ env.NODE_VERSION }}
          cache: 'npm'
          
      - name: Install dependencies
        run: npm ci
        
      - name: Build applications
        run: npm run build
        
      - name: Install k6
        run: |
          sudo gpg -k
          sudo gpg --no-default-keyring --keyring /usr/share/keyrings/k6-archive-keyring.gpg --keyserver hkp://keyserver.ubuntu.com:80 --recv-keys C5AD17C747E3415A3642D57D77C6C491D6AC1D69
          echo "deb [signed-by=/usr/share/keyrings/k6-archive-keyring.gpg] https://dl.k6.io/deb stable main" | sudo tee /etc/apt/sources.list.d/k6.list
          sudo apt-get update
          sudo apt-get install k6
          
      - name: Start test environment
        run: |
          # Start API and frontend for load testing
          cd apps/api && npm start &
          cd apps/web && npm run preview &
          sleep 15
          
      - name: Run load tests
        run: k6 run scripts/k6-load.js
        env:
          API_BASE_URL: http://localhost:3001
          
      - name: Upload performance results
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: performance-test-results
          path: |
            k6-results.json
            performance-report.html
          retention-days: 30

  # Job 6: Build Docker Images
  build-images:
    name: Build Docker Images
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [code-quality, security-scan, build-test]
    if: github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop')
    
    outputs:
      api-image: ${{ steps.meta-api.outputs.tags }}
      web-image: ${{ steps.meta-web.outputs.tags }}
      
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          driver-opts: |
            network=host
        
      - name: Log in to Container Registry
        uses: docker/login-action@v3
        with:
          registry: ${{ env.REGISTRY }}
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}
          
      - name: Extract API metadata
        id: meta-api
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-api
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
          labels: |
            org.opencontainers.image.title=Metamorphic Reactor API
            org.opencontainers.image.description=Dual-agent AI system backend API
            
      - name: Build and push API image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/api/Dockerfile
          target: production
          push: true
          tags: ${{ steps.meta-api.outputs.tags }}
          labels: ${{ steps.meta-api.outputs.labels }}
          cache-from: type=gha,scope=api
          cache-to: type=gha,mode=max,scope=api
          platforms: linux/amd64,linux/arm64
          
      - name: Extract Web metadata
        id: meta-web
        uses: docker/metadata-action@v5
        with:
          images: ${{ env.REGISTRY }}/${{ env.IMAGE_NAME }}-web
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=sha,prefix={{branch}}-
            type=raw,value=latest,enable={{is_default_branch}}
          labels: |
            org.opencontainers.image.title=Metamorphic Reactor Web
            org.opencontainers.image.description=Dual-agent AI system frontend
            
      - name: Build and push Web image
        uses: docker/build-push-action@v5
        with:
          context: .
          file: apps/web/Dockerfile
          target: production
          push: true
          tags: ${{ steps.meta-web.outputs.tags }}
          labels: ${{ steps.meta-web.outputs.labels }}
          cache-from: type=gha,scope=web
          cache-to: type=gha,mode=max,scope=web
          platforms: linux/amd64,linux/arm64

  # Job 7: Deploy to Staging
  deploy-staging:
    name: Deploy to Staging
    runs-on: ubuntu-latest
    timeout-minutes: 10
    needs: [build-images, e2e-tests, performance-test]
    if: github.ref == 'refs/heads/develop' && github.event_name == 'push'
    environment: staging
    
    steps:
      - name: Deploy to staging
        run: |
          echo "🚀 Deploying to staging environment"
          echo "API Image: ${{ needs.build-images.outputs.api-image }}"
          echo "Web Image: ${{ needs.build-images.outputs.web-image }}"
          # Add actual deployment commands here
          
      - name: Run smoke tests
        run: |
          echo "🧪 Running smoke tests against staging"
          # Add smoke test commands here
          
      - name: Notify deployment
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Staging deployment successful"
          else
            echo "❌ Staging deployment failed"
          fi

  # Job 8: Deploy to Production
  deploy-production:
    name: Deploy to Production
    runs-on: ubuntu-latest
    timeout-minutes: 15
    needs: [build-images, e2e-tests, performance-test]
    if: github.ref == 'refs/heads/main' && github.event_name == 'push'
    environment: production
    
    steps:
      - name: Deploy to production
        run: |
          echo "🚀 Deploying to production environment"
          echo "API Image: ${{ needs.build-images.outputs.api-image }}"
          echo "Web Image: ${{ needs.build-images.outputs.web-image }}"
          # Add actual deployment commands here
          
      - name: Run production smoke tests
        run: |
          echo "🧪 Running smoke tests against production"
          # Add production smoke test commands here
          
      - name: Notify deployment
        if: always()
        run: |
          if [ "${{ job.status }}" == "success" ]; then
            echo "✅ Production deployment successful"
            # Send success notification
          else
            echo "❌ Production deployment failed"
            # Send failure notification
          fi

  # Job 9: Cleanup
  cleanup:
    name: Cleanup
    runs-on: ubuntu-latest
    if: always()
    needs: [deploy-staging, deploy-production]
    
    steps:
      - name: Clean up old artifacts
        run: |
          echo "🧹 Cleaning up old artifacts and images"
          # Add cleanup commands here
