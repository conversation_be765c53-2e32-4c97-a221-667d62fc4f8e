
import { useState, useRef, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Switch } from '@/components/ui/switch';
import { Label } from '@/components/ui/label';
import { FileText, Code, Eye, Download, Copy, GitBranch, Zap, Activity, Wifi, WifiOff } from 'lucide-react';
import { DiffEditor } from '@monaco-editor/react';
import * as monaco from 'monaco-editor';
import { applyPatchOperations, createSimpleDiff, generateHtmlDiff, diffStyles } from '@/lib/diffUtils';
import { useWebSocketStatus } from '@/hooks/useWebSocket';

interface DiffViewerProps {
  diffContent?: string;
  patch?: any;
  originalCode?: string;
  modifiedCode?: string;
  language?: string;
  onCreatePR?: () => void;
  onDownload?: () => void;
  isStreaming?: boolean;
  streamingProgress?: number;
}

export const DiffViewer = ({
  diffContent,
  patch,
  originalCode = '',
  modifiedCode = '',
  language = 'typescript',
  onCreatePR,
  onDownload,
  isStreaming = false,
  streamingProgress = 0
}: DiffViewerProps) => {
  const [viewMode, setViewMode] = useState<'diff' | 'patch' | 'preview'>('diff');
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [intralineHighlights, setIntralineHighlights] = useState(true);
  const diffEditorRef = useRef<monaco.editor.IStandaloneDiffEditor | null>(null);
  const [copied, setCopied] = useState(false);
  const { isConnected, lastMessage, connectionStatus } = useWebSocketStatus();

  // Handle copy to clipboard
  const handleCopy = async (content: string) => {
    try {
      await navigator.clipboard.writeText(content);
      setCopied(true);
      setTimeout(() => setCopied(false), 2000);
    } catch (err) {
      console.error('Failed to copy:', err);
    }
  };

  // Handle Monaco diff editor mount
  const handleDiffEditorDidMount = (editor: monaco.editor.IStandaloneDiffEditor) => {
    diffEditorRef.current = editor;

    // Configure diff editor options with enhanced features
    editor.updateOptions({
      fontSize: 14,
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      automaticLayout: true,
      readOnly: true,
      renderSideBySide: true,
      ignoreTrimWhitespace: false,
      renderIndicators: true,
      originalEditable: false,
      modifiedEditable: false,
      wordWrap: 'on',
      lineNumbers: 'on',
      glyphMargin: true,
      folding: true,
      lineDecorationsWidth: 10,
      lineNumbersMinChars: 3,
      // Enhanced diff features
      renderWhitespace: 'boundary',
      renderLineHighlight: 'all',
      diffWordWrap: 'on',
      maxComputationTime: 5000,
      maxFileSize: 50,
      // Intraline diff configuration
      enableSplitViewResizing: true,
      renderOverviewRuler: true,
      diffCodeLens: true,
    });

    // Add custom decorations for enhanced diff visualization
    if (intralineHighlights) {
      setupIntralineHighlights(editor);
    }
  };

  // Setup intraline highlights for character-level differences
  const setupIntralineHighlights = (editor: monaco.editor.IStandaloneDiffEditor) => {
    const originalModel = editor.getOriginalEditor().getModel();
    const modifiedModel = editor.getModifiedEditor().getModel();

    if (originalModel && modifiedModel) {
      // Enhanced diff computation with character-level precision
      const originalText = originalModel.getValue();
      const modifiedText = modifiedModel.getValue();

      // Use Monaco's built-in diff algorithm for precise character differences
      const diffResult = monaco.editor.createDiffEditor(document.createElement('div'), {}).computeDiff(originalText, modifiedText);

      // Apply custom decorations for intraline changes
      if (diffResult) {
        const decorations = createIntralineDecorations(diffResult);
        editor.getOriginalEditor().deltaDecorations([], decorations.original);
        editor.getModifiedEditor().deltaDecorations([], decorations.modified);
      }
    }
  };

  // Create decorations for intraline highlights
  const createIntralineDecorations = (diffResult: any) => {
    const originalDecorations: monaco.editor.IModelDeltaDecoration[] = [];
    const modifiedDecorations: monaco.editor.IModelDeltaDecoration[] = [];

    // Add character-level highlight decorations
    // This would be implemented based on the specific diff algorithm results

    return {
      original: originalDecorations,
      modified: modifiedDecorations
    };
  };

  // Generate sample code for preview if not provided
  const getOriginalCode = () => {
    if (originalCode) return originalCode;
    if (patch?.operations) {
      // Try to reconstruct original from patch operations
      const sampleCode = '// Original code before transformation\n// This would be the actual file content\n\nfunction example() {\n  console.log("Before transformation");\n}';
      return sampleCode;
    }
    return '';
  };

  const getModifiedCode = () => {
    if (modifiedCode) return modifiedCode;
    if (patch?.operations) {
      // Try to apply patch operations to show result
      const originalSample = getOriginalCode();
      if (originalSample) {
        try {
          const { modified } = applyPatchOperations(originalSample, patch);
          return modified;
        } catch (error) {
          console.warn('Failed to apply patch operations:', error);
          return '// Modified code after transformation\n// This shows the result of applying the patch\n\nfunction example() {\n  console.log("After transformation");\n  // Additional optimizations applied\n}';
        }
      }
    }
    return '';
  };

  if (!diffContent && !patch && !originalCode && !modifiedCode) {
    return (
      <div className="h-full bg-slate-900 flex items-center justify-center">
        <div className="text-center text-slate-500">
          <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-slate-800 flex items-center justify-center">
            <div className="w-8 h-8 border-2 border-slate-600 rounded-sm flex items-center justify-center">
              <div className="w-4 h-4 bg-slate-600 rounded-sm"></div>
            </div>
          </div>
          <p className="text-lg font-medium">No changes available</p>
          <p className="text-sm">Run the reactor loop to see code transformations</p>
        </div>
      </div>
    );
  }

  const renderMonacoDiffView = () => {
    const original = getOriginalCode();
    const modified = getModifiedCode();

    if (!original && !modified) return null;

    return (
      <div className="h-full">
        <DiffEditor
          height="100%"
          language={language}
          original={original}
          modified={modified}
          onMount={handleDiffEditorDidMount}
          theme="vs-dark"
          options={{
            fontSize: 14,
            fontFamily: 'JetBrains Mono, Consolas, Monaco, monospace',
            fontLigatures: true,
            renderSideBySide: true,
            ignoreTrimWhitespace: false,
            renderIndicators: true,
            originalEditable: false,
            modifiedEditable: false,
            readOnly: true,
            minimap: { enabled: true },
            scrollBeyondLastLine: false,
            automaticLayout: true,
            wordWrap: 'on',
            lineNumbers: 'on',
            glyphMargin: true,
            folding: true,
            contextmenu: true,
            selectOnLineNumbers: true,
            roundedSelection: false,
            cursorStyle: 'line',
            smoothScrolling: true,
            renderWhitespace: 'selection',
            padding: { top: 16, bottom: 16 },
          }}
          loading={
            <div className="h-full bg-slate-900 flex items-center justify-center">
              <div className="text-slate-400">Loading diff editor...</div>
            </div>
          }
        />
      </div>
    );
  };

  const renderFallbackDiffView = () => {
    const original = getOriginalCode();
    const modified = getModifiedCode();

    if (!original && !modified) return null;

    try {
      const diffOps = createSimpleDiff(original, modified);
      const htmlDiff = generateHtmlDiff(diffOps);

      return (
        <div className="h-full overflow-auto">
          <style dangerouslySetInnerHTML={{ __html: diffStyles }} />
          <div className="diff-container">
            <div className="text-xs text-slate-400 mb-2 px-4 pt-2">
              Fallback diff view (Monaco editor unavailable)
            </div>
            <div dangerouslySetInnerHTML={{ __html: htmlDiff }} />
          </div>
        </div>
      );
    } catch (error) {
      console.error('Failed to generate fallback diff:', error);
      return (
        <div className="h-full bg-slate-900 flex items-center justify-center">
          <div className="text-center text-slate-500">
            <p>Unable to display diff</p>
            <p className="text-xs mt-1">Check console for details</p>
          </div>
        </div>
      );
    }
  };

  const renderDiffView = () => {
    // If we have original/modified code, try Monaco diff editor first
    if (originalCode || modifiedCode || patch) {
      try {
        return renderMonacoDiffView();
      } catch (error) {
        console.warn('Monaco diff editor failed, using fallback:', error);
        return renderFallbackDiffView();
      }
    }

    // Fallback to traditional diff view
    if (!diffContent) return null;

    const lines = diffContent.split('\n');

    return (
      <div className="p-4 font-mono text-sm">
        <div className="space-y-0">
          {lines.map((line, index) => {
            let className = "block px-3 py-1 border-l-2 ";
            let lineNumber = "";

            if (line.startsWith('---') || line.startsWith('+++')) {
              className += "text-slate-400 font-bold bg-slate-800/50 border-l-slate-600";
            } else if (line.startsWith('@@')) {
              className += "text-indigo-400 bg-indigo-900/20 border-l-indigo-500";
              lineNumber = "@@";
            } else if (line.startsWith('+')) {
              className += "text-green-400 bg-green-900/20 border-l-green-500";
              lineNumber = "+";
            } else if (line.startsWith('-')) {
              className += "text-red-400 bg-red-900/20 border-l-red-500";
              lineNumber = "-";
            } else {
              className += "text-slate-300 border-l-slate-700";
              lineNumber = " ";
            }

            return (
              <div key={index} className={className}>
                <span className="inline-block w-6 text-center text-xs opacity-60 mr-2">
                  {lineNumber}
                </span>
                <span className="whitespace-pre-wrap">
                  {line.replace(/^[+\-\s]/, '') || ' '}
                </span>
              </div>
            );
          })}
        </div>
      </div>
    );
  };

  const renderPatchView = () => {
    if (!patch) return null;

    return (
      <div className="p-4">
        <div className="space-y-4">
          <div className="bg-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-medium text-slate-300 mb-2">Patch Description</h3>
            <p className="text-sm text-slate-400">{patch.description || 'No description available'}</p>
            {patch.confidence && (
              <div className="mt-2">
                <Badge className="bg-indigo-600/20 text-indigo-300 border-indigo-500/30">
                  Confidence: {(patch.confidence * 100).toFixed(1)}%
                </Badge>
              </div>
            )}
          </div>

          <div className="bg-slate-800 rounded-lg p-4">
            <h3 className="text-sm font-medium text-slate-300 mb-2">Operations</h3>
            <div className="space-y-2">
              {patch.operations?.map((op: any, index: number) => (
                <div key={index} className="bg-slate-900 rounded p-3 font-mono text-xs">
                  <div className="flex items-center space-x-2 mb-1">
                    <Badge variant="outline" className="text-xs">
                      {op.op.toUpperCase()}
                    </Badge>
                    <span className="text-slate-400">{op.path}</span>
                  </div>
                  {op.value && (
                    <pre className="text-slate-300 whitespace-pre-wrap">
                      {JSON.stringify(op.value, null, 2)}
                    </pre>
                  )}
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="h-full bg-slate-900 flex flex-col">
      {/* Header with View Mode Tabs and Actions */}
      <div className="flex items-center justify-between p-2 border-b border-slate-800">
        <div className="flex items-center space-x-1">
          <Button
            variant={viewMode === 'diff' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('diff')}
            disabled={!diffContent && !originalCode && !modifiedCode}
            className="text-xs"
          >
            <FileText className="w-3 h-3 mr-1" />
            Diff
          </Button>
          <Button
            variant={viewMode === 'patch' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('patch')}
            disabled={!patch}
            className="text-xs"
          >
            <Code className="w-3 h-3 mr-1" />
            Patch
          </Button>
          <Button
            variant={viewMode === 'preview' ? 'default' : 'ghost'}
            size="sm"
            onClick={() => setViewMode('preview')}
            disabled={!patch}
            className="text-xs"
          >
            <Eye className="w-3 h-3 mr-1" />
            Preview
          </Button>
        </div>

        {/* Enhanced Controls */}
        <div className="flex items-center space-x-3">
          {/* Live Stream Status Badge */}
          <div className="flex items-center space-x-2">
            <Badge
              variant={isConnected ? "default" : "secondary"}
              className={`text-xs ${
                isConnected
                  ? "bg-green-600/20 text-green-300 border-green-500/30"
                  : "bg-red-600/20 text-red-300 border-red-500/30"
              }`}
            >
              {isConnected ? (
                <Wifi className="w-3 h-3 mr-1" />
              ) : (
                <WifiOff className="w-3 h-3 mr-1" />
              )}
              {isConnected ? 'Live' : 'Offline'}
            </Badge>

            {isStreaming && (
              <Badge className="bg-blue-600/20 text-blue-300 border-blue-500/30 text-xs">
                <Activity className="w-3 h-3 mr-1 animate-pulse" />
                Streaming {streamingProgress}%
              </Badge>
            )}
          </div>

          {/* Intraline Highlights Toggle */}
          <div className="flex items-center space-x-2">
            <Label htmlFor="intraline-toggle" className="text-xs text-slate-400">
              Intraline
            </Label>
            <Switch
              id="intraline-toggle"
              checked={intralineHighlights}
              onCheckedChange={(checked) => {
                setIntralineHighlights(checked);
                if (diffEditorRef.current) {
                  if (checked) {
                    setupIntralineHighlights(diffEditorRef.current);
                  } else {
                    // Clear decorations
                    diffEditorRef.current.getOriginalEditor().deltaDecorations([], []);
                    diffEditorRef.current.getModifiedEditor().deltaDecorations([], []);
                  }
                }
              }}
              className="scale-75"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex items-center space-x-1">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleCopy(viewMode === 'diff' ? (diffContent || getModifiedCode()) : JSON.stringify(patch, null, 2))}
              className="text-xs"
            >
              <Copy className="w-3 h-3 mr-1" />
              {copied ? 'Copied!' : 'Copy'}
            </Button>

          {onDownload && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onDownload}
              className="text-xs"
            >
              <Download className="w-3 h-3 mr-1" />
              Download
            </Button>
          )}

          {onCreatePR && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onCreatePR}
              className="text-xs bg-indigo-600/20 text-indigo-300 border-indigo-500/30 hover:bg-indigo-600/30"
            >
              <GitBranch className="w-3 h-3 mr-1" />
              Create PR
            </Button>
          )}
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="flex-1 overflow-auto">
        {viewMode === 'diff' && renderDiffView()}
        {viewMode === 'patch' && renderPatchView()}
        {viewMode === 'preview' && (
          <div className="p-4 text-center text-slate-500">
            <p>Preview mode coming soon</p>
          </div>
        )}
      </div>
    </div>
  );
};
