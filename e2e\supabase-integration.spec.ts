import { test, expect } from '@playwright/test';

test.describe('Supabase Integration E2E', () => {
  test.beforeEach(async ({ page }) => {
    // Mock Supabase responses for testing
    await page.route('**/functions/v1/ai-loop', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          finalPatch: {
            operations: [
              { op: 'add', path: '/optimization', value: 'memoization' }
            ],
            description: 'Added memoization for performance'
          },
          finalScore: 0.95,
          iterations: 3,
          logs: [
            {
              iteration: 1,
              plan: 'Adding memoization',
              critique: 'Good approach, needs refinement',
              score: 0.8,
              patch: { operations: [] }
            }
          ],
          completed: true
        })
      });
    });

    await page.route('**/functions/v1/github-oauth', async route => {
      if (route.request().method() === 'GET') {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            hasToken: false,
            tokenInfo: null
          })
        });
      } else {
        await route.fulfill({
          status: 200,
          contentType: 'application/json',
          body: JSON.stringify({
            success: true,
            user: {
              login: 'testuser',
              name: 'Test User',
              avatar_url: 'https://github.com/avatar.jpg'
            }
          })
        });
      }
    });

    await page.route('**/functions/v1/create-pr', async route => {
      await route.fulfill({
        status: 200,
        contentType: 'application/json',
        body: JSON.stringify({
          success: true,
          pr: {
            url: 'https://github.com/test/repo/pull/123',
            number: 123,
            branch: 'reactor/test-branch',
            title: 'feat: reactor auto patch'
          }
        })
      });
    });

    // Mock Supabase auth
    await page.addInitScript(() => {
      // Generate a proper test JWT token for E2E testing
      const testUser = {
        id: 'e2e-test-user-123',
        email: '<EMAIL>',
        role: 'authenticated',
        app_metadata: { role: 'authenticated' },
        user_metadata: { name: 'E2E Test User' }
      };

      // Create a valid JWT token for testing (simplified for E2E)
      const testJWT = btoa(JSON.stringify({
        sub: testUser.id,
        email: testUser.email,
        role: testUser.role,
        aud: 'authenticated',
        iss: 'https://test.supabase.co/auth/v1',
        iat: Math.floor(Date.now() / 1000),
        exp: Math.floor(Date.now() / 1000) + 3600, // 1 hour
        app_metadata: testUser.app_metadata,
        user_metadata: testUser.user_metadata
      }));

      window.localStorage.setItem('supabase.auth.token', JSON.stringify({
        access_token: `header.${testJWT}.signature`,
        user: testUser
      }));
    });
  });

  test('should run Supabase reactor loop successfully', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Enter a test prompt
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Optimize this function with memoization');
    
    // Start the reactor loop
    await page.click('text=Start');
    
    // Should show running state
    await expect(page.locator('text=Running')).toBeVisible();
    
    // Wait for completion
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Should show patch ready badge
    await expect(page.locator('text=JSON Patch Ready')).toBeVisible();
    
    // Should show final score in stream
    await expect(page.locator('text=Final score: 95.0%')).toBeVisible();
  });

  test('should display Monaco diff editor with patch results', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Run a loop first
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Add error handling');
    await page.click('text=Start');
    
    // Wait for completion
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Check that Monaco diff editor is rendered
    await expect(page.locator('.monaco-diff-editor')).toBeVisible();
    
    // Should be able to switch between diff and patch views
    await page.click('text=Patch');
    await expect(page.locator('text=Patch Description')).toBeVisible();
    await expect(page.locator('text=Added memoization for performance')).toBeVisible();
    
    // Switch back to diff view
    await page.click('text=Diff');
    await expect(page.locator('.monaco-diff-editor')).toBeVisible();
  });

  test('should handle GitHub OAuth flow', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Should show "Connect GitHub" button initially
    await expect(page.locator('text=Connect GitHub')).toBeVisible();
    
    // Click connect GitHub (this would normally redirect to GitHub)
    await page.click('text=Connect GitHub');
    
    // In a real test, this would involve OAuth flow
    // For now, we'll simulate the connected state
    await page.evaluate(() => {
      window.localStorage.setItem('github_connected', 'true');
    });
    
    await page.reload();
    
    // Should now show "Connected" status
    await expect(page.locator('text=Connected')).toBeVisible();
  });

  test('should create GitHub PR after successful loop', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Simulate GitHub connection
    await page.evaluate(() => {
      window.localStorage.setItem('github_connected', 'true');
    });
    
    // Run reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Refactor for better performance');
    await page.click('text=Start');
    
    // Wait for completion
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Should show Create PR button in diff viewer
    await expect(page.locator('text=Create PR')).toBeVisible();
    
    // Click Create PR
    await page.click('text=Create PR');
    
    // Should show success message
    await expect(page.locator('text=Pull Request created successfully')).toBeVisible();
  });

  test('should handle copy functionality in diff viewer', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Run reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Add logging');
    await page.click('text=Start');
    
    // Wait for completion
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Click copy button
    await page.click('text=Copy');
    
    // Should show "Copied!" feedback
    await expect(page.locator('text=Copied!')).toBeVisible();
    
    // Should revert back to "Copy"
    await expect(page.locator('text=Copy')).toBeVisible({ timeout: 3000 });
  });

  test('should handle download functionality', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Run reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Optimize algorithm');
    await page.click('text=Start');
    
    // Wait for completion
    await expect(page.locator('text=Reactor loop completed')).toBeVisible({ timeout: 10000 });
    
    // Set up download listener
    const downloadPromise = page.waitForEvent('download');
    
    // Click download button in diff viewer
    await page.click('text=Download');
    
    // Should trigger download
    const download = await downloadPromise;
    expect(download.suggestedFilename()).toMatch(/reactor-patch\.json|optimized-code\.js/);
  });

  test('should handle authentication errors gracefully', async ({ page }) => {
    // Clear auth token to simulate unauthenticated state
    await page.addInitScript(() => {
      window.localStorage.removeItem('supabase.auth.token');
    });
    
    // Mock auth error response
    await page.route('**/functions/v1/ai-loop', async route => {
      await route.fulfill({
        status: 401,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Not authenticated'
        })
      });
    });
    
    await page.goto('/dashboard');
    
    // Try to run reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Test prompt');
    await page.click('text=Start');
    
    // Should show authentication error
    await expect(page.locator('text=Not authenticated')).toBeVisible();
  });

  test('should handle API errors gracefully', async ({ page }) => {
    // Mock API error response
    await page.route('**/functions/v1/ai-loop', async route => {
      await route.fulfill({
        status: 500,
        contentType: 'application/json',
        body: JSON.stringify({
          error: 'Internal server error'
        })
      });
    });
    
    await page.goto('/dashboard');
    
    // Try to run reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Test prompt');
    await page.click('text=Start');
    
    // Should show error message
    await expect(page.locator('text=Internal server error')).toBeVisible();
  });

  test('should stop reactor loop when requested', async ({ page }) => {
    await page.goto('/dashboard');
    
    // Start reactor loop
    const editor = page.locator('.monaco-editor');
    await editor.click();
    await page.keyboard.press('Control+A');
    await page.keyboard.type('Long running task');
    await page.click('text=Start');
    
    // Should show running state
    await expect(page.locator('text=Running')).toBeVisible();
    
    // Click stop
    await page.click('text=Stop');
    
    // Should show stopped message
    await expect(page.locator('text=stopped by user')).toBeVisible();
    
    // Should not be running anymore
    await expect(page.locator('text=Running')).not.toBeVisible();
  });
});
