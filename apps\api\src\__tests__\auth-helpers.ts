import jwt from 'jsonwebtoken';
import { createClient } from '@supabase/supabase-js';

// Test environment configuration
const TEST_JWT_SECRET = 'test-jwt-secret-for-testing-only';
const TEST_SUPABASE_URL = process.env.SUPABASE_URL || 'https://test.supabase.co';
const TEST_SUPABASE_SERVICE_KEY = process.env.SUPABASE_SERVICE_ROLE_KEY || 'test-service-key';

// Test user data
export const TEST_USERS = {
  regular: {
    id: 'test-user-regular-123',
    email: '<EMAIL>',
    role: 'authenticated',
    app_metadata: { role: 'authenticated' },
    user_metadata: { name: 'Test User' }
  },
  admin: {
    id: 'test-user-admin-456',
    email: '<EMAIL>',
    role: 'admin',
    app_metadata: { role: 'admin' },
    user_metadata: { name: 'Admin User' }
  },
  service: {
    id: 'service',
    role: 'service_role'
  }
};

/**
 * Generate a valid JWT token for testing
 * 
 * @param user User data to include in the token
 * @param expiresIn Token expiration time (default: 1 hour)
 * @returns JWT token string
 */
export function generateTestJWT(
  user: typeof TEST_USERS.regular | typeof TEST_USERS.admin = TEST_USERS.regular,
  expiresIn: string = '1h'
): string {
  const payload = {
    sub: user.id,
    email: user.email,
    role: user.role,
    aud: 'authenticated',
    iss: `${TEST_SUPABASE_URL}/auth/v1`,
    iat: Math.floor(Date.now() / 1000),
    exp: Math.floor(Date.now() / 1000) + (expiresIn === '1h' ? 3600 : 300), // 1 hour or 5 minutes
    app_metadata: user.app_metadata || {},
    user_metadata: user.user_metadata || {}
  };

  return jwt.sign(payload, TEST_JWT_SECRET, { algorithm: 'HS256' });
}

/**
 * Generate an expired JWT token for testing
 * 
 * @param user User data to include in the token
 * @returns Expired JWT token string
 */
export function generateExpiredTestJWT(
  user: typeof TEST_USERS.regular = TEST_USERS.regular
): string {
  const payload = {
    sub: user.id,
    email: user.email,
    role: user.role,
    aud: 'authenticated',
    iss: `${TEST_SUPABASE_URL}/auth/v1`,
    iat: Math.floor(Date.now() / 1000) - 7200, // 2 hours ago
    exp: Math.floor(Date.now() / 1000) - 3600, // 1 hour ago (expired)
    app_metadata: user.app_metadata || {},
    user_metadata: user.user_metadata || {}
  };

  return jwt.sign(payload, TEST_JWT_SECRET, { algorithm: 'HS256' });
}

/**
 * Generate an invalid JWT token for testing
 * 
 * @returns Invalid JWT token string
 */
export function generateInvalidTestJWT(): string {
  return 'invalid.jwt.token';
}

/**
 * Get service role key for testing admin endpoints
 * 
 * @returns Service role key
 */
export function getTestServiceKey(): string {
  return TEST_SUPABASE_SERVICE_KEY;
}

/**
 * Create authorization header for testing
 * 
 * @param token JWT token or service key
 * @returns Authorization header value
 */
export function createAuthHeader(token: string): string {
  return `Bearer ${token}`;
}

/**
 * Mock Supabase auth.getUser response for testing
 * 
 * @param user User data to return
 * @param error Error to return (if any)
 * @returns Mock response object
 */
export function mockSupabaseAuthResponse(
  user: typeof TEST_USERS.regular | null = TEST_USERS.regular,
  error: any = null
) {
  return {
    data: { user },
    error
  };
}

/**
 * Setup test environment with proper JWT secret
 */
export function setupTestEnvironment(): void {
  // Set test environment variables
  process.env.NODE_ENV = 'test';
  process.env.SUPABASE_URL = TEST_SUPABASE_URL;
  process.env.SUPABASE_SERVICE_ROLE_KEY = TEST_SUPABASE_SERVICE_KEY;
  process.env.JWT_SECRET = TEST_JWT_SECRET;
}

/**
 * Authentication test scenarios for reuse across test files
 */
export const AUTH_TEST_SCENARIOS = {
  validToken: {
    description: 'should accept valid JWT token',
    token: () => generateTestJWT(TEST_USERS.regular),
    expectedStatus: 200,
    expectedUser: TEST_USERS.regular
  },
  
  adminToken: {
    description: 'should accept valid admin JWT token',
    token: () => generateTestJWT(TEST_USERS.admin),
    expectedStatus: 200,
    expectedUser: TEST_USERS.admin
  },
  
  expiredToken: {
    description: 'should reject expired JWT token',
    token: () => generateExpiredTestJWT(TEST_USERS.regular),
    expectedStatus: 401,
    expectedError: 'EXPIRED_TOKEN'
  },
  
  invalidToken: {
    description: 'should reject invalid JWT token',
    token: () => generateInvalidTestJWT(),
    expectedStatus: 401,
    expectedError: 'INVALID_TOKEN'
  },
  
  missingToken: {
    description: 'should reject missing authorization header',
    token: () => '',
    expectedStatus: 401,
    expectedError: 'MISSING_AUTH_HEADER'
  },
  
  invalidFormat: {
    description: 'should reject invalid authorization format',
    token: () => 'InvalidFormat token-here',
    expectedStatus: 401,
    expectedError: 'INVALID_AUTH_FORMAT'
  },
  
  serviceKey: {
    description: 'should accept valid service role key',
    token: () => getTestServiceKey(),
    expectedStatus: 200,
    expectedRole: 'service_role'
  }
};

/**
 * Helper to test authentication on an endpoint
 * 
 * @param request Supertest request function
 * @param method HTTP method
 * @param endpoint API endpoint
 * @param scenarios Array of test scenarios to run
 */
export async function testEndpointAuthentication(
  request: any,
  method: 'get' | 'post' | 'put' | 'delete',
  endpoint: string,
  scenarios: (keyof typeof AUTH_TEST_SCENARIOS)[] = ['validToken', 'expiredToken', 'invalidToken', 'missingToken'],
  requestBody?: any
) {
  for (const scenarioKey of scenarios) {
    const scenario = AUTH_TEST_SCENARIOS[scenarioKey];
    
    it(scenario.description, async () => {
      const token = scenario.token();
      let req = request[method](endpoint);
      
      if (token && !token.includes('InvalidFormat')) {
        req = req.set('Authorization', createAuthHeader(token));
      } else if (token.includes('InvalidFormat')) {
        req = req.set('Authorization', token);
      }
      
      if (requestBody && (method === 'post' || method === 'put')) {
        req = req.send(requestBody);
      }
      
      const response = await req.expect(scenario.expectedStatus);
      
      if (scenario.expectedError) {
        expect(response.body.code).toBe(scenario.expectedError);
      }
      
      if (scenario.expectedUser && scenario.expectedStatus === 200) {
        // Additional assertions can be added here based on the endpoint
      }
    });
  }
}

/**
 * Mock the Supabase client for testing
 */
export function mockSupabaseClient() {
  const mockClient = {
    auth: {
      getUser: jest.fn()
    }
  };
  
  return mockClient;
}

export default {
  generateTestJWT,
  generateExpiredTestJWT,
  generateInvalidTestJWT,
  getTestServiceKey,
  createAuthHeader,
  mockSupabaseAuthResponse,
  setupTestEnvironment,
  testEndpointAuthentication,
  mockSupabaseClient,
  TEST_USERS,
  AUTH_TEST_SCENARIOS
};
